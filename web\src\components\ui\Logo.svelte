<script>
  export let className = '';
  export let fill = '';
  export let stroke = 'currentColor';
</script>

<svg
  xmlns="http://www.w3.org/2000/svg"
  class={className}
  {fill}
  viewBox="0 0 256 256"
  height="32"
  width="32"
  {stroke}
  {...$$restProps}>
  <rect height="240" {stroke} stroke-width="26" width="240" x="8" y="8" rx="26" />
  <path
    d="M80 130 L110 160 L180 90"
    fill="none"
    {stroke}
    stroke-dasharray="300"
    stroke-dashoffset="300"
    stroke-linecap="round"
    stroke-linejoin="round"
    stroke-width="26">
    <animate
      attributeName="stroke-dashoffset"
      begin="0.2s"
      dur="0.6s"
      fill="freeze"
      from="300"
      to="0" />
  </path></svg>
