/**
 * @license @lucide/svelte v0.482.0 - ISC
 *
 * ISC License
 * 
 * Copyright (c) for portions of Lucide are held by <PERSON> 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
 * 
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
 * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 * 
 */
// AArrowDown aliases
export { default as LucideAArrowDown } from '../icons/a-arrow-down.js';
// AArrowUp aliases
export { default as LucideAArrowUp } from '../icons/a-arrow-up.js';
// ALargeSmall aliases
export { default as LucideALargeSmall } from '../icons/a-large-small.js';
// Accessibility aliases
export { default as LucideAccessibility } from '../icons/accessibility.js';
// Activity aliases
export { default as LucideActivity } from '../icons/activity.js';
// AirVent aliases
export { default as LucideAirVent } from '../icons/air-vent.js';
// Airplay aliases
export { default as LucideAirplay } from '../icons/airplay.js';
// AlarmClockOff aliases
export { default as LucideAlarmClockOff } from '../icons/alarm-clock-off.js';
// AlarmClock aliases
export { default as LucideAlarmClock } from '../icons/alarm-clock.js';
// AlarmSmoke aliases
export { default as LucideAlarmSmoke } from '../icons/alarm-smoke.js';
// Album aliases
export { default as LucideAlbum } from '../icons/album.js';
// AlignCenterHorizontal aliases
export { default as LucideAlignCenterHorizontal } from '../icons/align-center-horizontal.js';
// AlignCenterVertical aliases
export { default as LucideAlignCenterVertical } from '../icons/align-center-vertical.js';
// AlignCenter aliases
export { default as LucideAlignCenter } from '../icons/align-center.js';
// AlignEndHorizontal aliases
export { default as LucideAlignEndHorizontal } from '../icons/align-end-horizontal.js';
// AlignEndVertical aliases
export { default as LucideAlignEndVertical } from '../icons/align-end-vertical.js';
// AlignHorizontalDistributeCenter aliases
export { default as LucideAlignHorizontalDistributeCenter } from '../icons/align-horizontal-distribute-center.js';
// AlignHorizontalDistributeEnd aliases
export { default as LucideAlignHorizontalDistributeEnd } from '../icons/align-horizontal-distribute-end.js';
// AlignHorizontalDistributeStart aliases
export { default as LucideAlignHorizontalDistributeStart } from '../icons/align-horizontal-distribute-start.js';
// AlignHorizontalJustifyCenter aliases
export { default as LucideAlignHorizontalJustifyCenter } from '../icons/align-horizontal-justify-center.js';
// AlignHorizontalJustifyEnd aliases
export { default as LucideAlignHorizontalJustifyEnd } from '../icons/align-horizontal-justify-end.js';
// AlignHorizontalSpaceAround aliases
export { default as LucideAlignHorizontalSpaceAround } from '../icons/align-horizontal-space-around.js';
// AlignHorizontalSpaceBetween aliases
export { default as LucideAlignHorizontalSpaceBetween } from '../icons/align-horizontal-space-between.js';
// AlignHorizontalJustifyStart aliases
export { default as LucideAlignHorizontalJustifyStart } from '../icons/align-horizontal-justify-start.js';
// AlignLeft aliases
export { default as LucideAlignLeft } from '../icons/align-left.js';
// AlignJustify aliases
export { default as LucideAlignJustify } from '../icons/align-justify.js';
// AlignRight aliases
export { default as LucideAlignRight } from '../icons/align-right.js';
// AlignStartHorizontal aliases
export { default as LucideAlignStartHorizontal } from '../icons/align-start-horizontal.js';
// AlignStartVertical aliases
export { default as LucideAlignStartVertical } from '../icons/align-start-vertical.js';
// AlignVerticalDistributeCenter aliases
export { default as LucideAlignVerticalDistributeCenter } from '../icons/align-vertical-distribute-center.js';
// AlignVerticalDistributeStart aliases
export { default as LucideAlignVerticalDistributeStart } from '../icons/align-vertical-distribute-start.js';
// AlignVerticalDistributeEnd aliases
export { default as LucideAlignVerticalDistributeEnd } from '../icons/align-vertical-distribute-end.js';
// AlignVerticalJustifyCenter aliases
export { default as LucideAlignVerticalJustifyCenter } from '../icons/align-vertical-justify-center.js';
// AlignVerticalJustifyEnd aliases
export { default as LucideAlignVerticalJustifyEnd } from '../icons/align-vertical-justify-end.js';
// AlignVerticalSpaceAround aliases
export { default as LucideAlignVerticalSpaceAround } from '../icons/align-vertical-space-around.js';
// AlignVerticalJustifyStart aliases
export { default as LucideAlignVerticalJustifyStart } from '../icons/align-vertical-justify-start.js';
// AlignVerticalSpaceBetween aliases
export { default as LucideAlignVerticalSpaceBetween } from '../icons/align-vertical-space-between.js';
// Ambulance aliases
export { default as LucideAmbulance } from '../icons/ambulance.js';
// Ampersand aliases
export { default as LucideAmpersand } from '../icons/ampersand.js';
// Ampersands aliases
export { default as LucideAmpersands } from '../icons/ampersands.js';
// Amphora aliases
export { default as LucideAmphora } from '../icons/amphora.js';
// Anchor aliases
export { default as LucideAnchor } from '../icons/anchor.js';
// Angry aliases
export { default as LucideAngry } from '../icons/angry.js';
// Annoyed aliases
export { default as LucideAnnoyed } from '../icons/annoyed.js';
// Antenna aliases
export { default as LucideAntenna } from '../icons/antenna.js';
// Anvil aliases
export { default as LucideAnvil } from '../icons/anvil.js';
// Aperture aliases
export { default as LucideAperture } from '../icons/aperture.js';
// AppWindowMac aliases
export { default as LucideAppWindowMac } from '../icons/app-window-mac.js';
// AppWindow aliases
export { default as LucideAppWindow } from '../icons/app-window.js';
// Apple aliases
export { default as LucideApple } from '../icons/apple.js';
// ArchiveRestore aliases
export { default as LucideArchiveRestore } from '../icons/archive-restore.js';
// ArchiveX aliases
export { default as LucideArchiveX } from '../icons/archive-x.js';
// Archive aliases
export { default as LucideArchive } from '../icons/archive.js';
// Armchair aliases
export { default as LucideArmchair } from '../icons/armchair.js';
// ArrowBigDownDash aliases
export { default as LucideArrowBigDownDash } from '../icons/arrow-big-down-dash.js';
// ArrowBigDown aliases
export { default as LucideArrowBigDown } from '../icons/arrow-big-down.js';
// ArrowBigLeftDash aliases
export { default as LucideArrowBigLeftDash } from '../icons/arrow-big-left-dash.js';
// ArrowBigLeft aliases
export { default as LucideArrowBigLeft } from '../icons/arrow-big-left.js';
// ArrowBigRightDash aliases
export { default as LucideArrowBigRightDash } from '../icons/arrow-big-right-dash.js';
// ArrowBigRight aliases
export { default as LucideArrowBigRight } from '../icons/arrow-big-right.js';
// ArrowBigUpDash aliases
export { default as LucideArrowBigUpDash } from '../icons/arrow-big-up-dash.js';
// ArrowBigUp aliases
export { default as LucideArrowBigUp } from '../icons/arrow-big-up.js';
// ArrowDownFromLine aliases
export { default as LucideArrowDownFromLine } from '../icons/arrow-down-from-line.js';
// ArrowDownLeft aliases
export { default as LucideArrowDownLeft } from '../icons/arrow-down-left.js';
// ArrowDownNarrowWide aliases
export { default as LucideArrowDownNarrowWide } from '../icons/arrow-down-narrow-wide.js';
// ArrowDownRight aliases
export { default as LucideArrowDownRight } from '../icons/arrow-down-right.js';
// ArrowDownToDot aliases
export { default as LucideArrowDownToDot } from '../icons/arrow-down-to-dot.js';
// ArrowDownToLine aliases
export { default as LucideArrowDownToLine } from '../icons/arrow-down-to-line.js';
// ArrowDownUp aliases
export { default as LucideArrowDownUp } from '../icons/arrow-down-up.js';
// ArrowDown aliases
export { default as LucideArrowDown } from '../icons/arrow-down.js';
// ArrowLeftFromLine aliases
export { default as LucideArrowLeftFromLine } from '../icons/arrow-left-from-line.js';
// ArrowLeftRight aliases
export { default as LucideArrowLeftRight } from '../icons/arrow-left-right.js';
// ArrowLeftToLine aliases
export { default as LucideArrowLeftToLine } from '../icons/arrow-left-to-line.js';
// ArrowLeft aliases
export { default as LucideArrowLeft } from '../icons/arrow-left.js';
// ArrowRightFromLine aliases
export { default as LucideArrowRightFromLine } from '../icons/arrow-right-from-line.js';
// ArrowRightLeft aliases
export { default as LucideArrowRightLeft } from '../icons/arrow-right-left.js';
// ArrowRightToLine aliases
export { default as LucideArrowRightToLine } from '../icons/arrow-right-to-line.js';
// ArrowRight aliases
export { default as LucideArrowRight } from '../icons/arrow-right.js';
// ArrowUpDown aliases
export { default as LucideArrowUpDown } from '../icons/arrow-up-down.js';
// ArrowUpFromDot aliases
export { default as LucideArrowUpFromDot } from '../icons/arrow-up-from-dot.js';
// ArrowUpFromLine aliases
export { default as LucideArrowUpFromLine } from '../icons/arrow-up-from-line.js';
// ArrowUpLeft aliases
export { default as LucideArrowUpLeft } from '../icons/arrow-up-left.js';
// ArrowUpRight aliases
export { default as LucideArrowUpRight } from '../icons/arrow-up-right.js';
// ArrowUpToLine aliases
export { default as LucideArrowUpToLine } from '../icons/arrow-up-to-line.js';
// ArrowUpWideNarrow aliases
export { default as LucideArrowUpWideNarrow } from '../icons/arrow-up-wide-narrow.js';
// ArrowUp aliases
export { default as LucideArrowUp } from '../icons/arrow-up.js';
// ArrowsUpFromLine aliases
export { default as LucideArrowsUpFromLine } from '../icons/arrows-up-from-line.js';
// Asterisk aliases
export { default as LucideAsterisk } from '../icons/asterisk.js';
// AtSign aliases
export { default as LucideAtSign } from '../icons/at-sign.js';
// AudioLines aliases
export { default as LucideAudioLines } from '../icons/audio-lines.js';
// Atom aliases
export { default as LucideAtom } from '../icons/atom.js';
// AudioWaveform aliases
export { default as LucideAudioWaveform } from '../icons/audio-waveform.js';
// Award aliases
export { default as LucideAward } from '../icons/award.js';
// Axe aliases
export { default as LucideAxe } from '../icons/axe.js';
// Baby aliases
export { default as LucideBaby } from '../icons/baby.js';
// Backpack aliases
export { default as LucideBackpack } from '../icons/backpack.js';
// BadgeAlert aliases
export { default as LucideBadgeAlert } from '../icons/badge-alert.js';
// BadgeCent aliases
export { default as LucideBadgeCent } from '../icons/badge-cent.js';
// BadgeDollarSign aliases
export { default as LucideBadgeDollarSign } from '../icons/badge-dollar-sign.js';
// BadgeHelp aliases
export { default as LucideBadgeHelp } from '../icons/badge-help.js';
// BadgeEuro aliases
export { default as LucideBadgeEuro } from '../icons/badge-euro.js';
// BadgeIndianRupee aliases
export { default as LucideBadgeIndianRupee } from '../icons/badge-indian-rupee.js';
// BadgeInfo aliases
export { default as LucideBadgeInfo } from '../icons/badge-info.js';
// BadgeJapaneseYen aliases
export { default as LucideBadgeJapaneseYen } from '../icons/badge-japanese-yen.js';
// BadgeMinus aliases
export { default as LucideBadgeMinus } from '../icons/badge-minus.js';
// BadgePercent aliases
export { default as LucideBadgePercent } from '../icons/badge-percent.js';
// BadgePlus aliases
export { default as LucideBadgePlus } from '../icons/badge-plus.js';
// BadgePoundSterling aliases
export { default as LucideBadgePoundSterling } from '../icons/badge-pound-sterling.js';
// BadgeRussianRuble aliases
export { default as LucideBadgeRussianRuble } from '../icons/badge-russian-ruble.js';
// BadgeSwissFranc aliases
export { default as LucideBadgeSwissFranc } from '../icons/badge-swiss-franc.js';
// BadgeX aliases
export { default as LucideBadgeX } from '../icons/badge-x.js';
// Badge aliases
export { default as LucideBadge } from '../icons/badge.js';
// BaggageClaim aliases
export { default as LucideBaggageClaim } from '../icons/baggage-claim.js';
// Ban aliases
export { default as LucideBan } from '../icons/ban.js';
// Banana aliases
export { default as LucideBanana } from '../icons/banana.js';
// Bandage aliases
export { default as LucideBandage } from '../icons/bandage.js';
// Banknote aliases
export { default as LucideBanknote } from '../icons/banknote.js';
// Baseline aliases
export { default as LucideBaseline } from '../icons/baseline.js';
// Barcode aliases
export { default as LucideBarcode } from '../icons/barcode.js';
// Bath aliases
export { default as LucideBath } from '../icons/bath.js';
// BatteryCharging aliases
export { default as LucideBatteryCharging } from '../icons/battery-charging.js';
// BatteryFull aliases
export { default as LucideBatteryFull } from '../icons/battery-full.js';
// BatteryLow aliases
export { default as LucideBatteryLow } from '../icons/battery-low.js';
// BatteryMedium aliases
export { default as LucideBatteryMedium } from '../icons/battery-medium.js';
// BatteryPlus aliases
export { default as LucideBatteryPlus } from '../icons/battery-plus.js';
// BatteryWarning aliases
export { default as LucideBatteryWarning } from '../icons/battery-warning.js';
// Battery aliases
export { default as LucideBattery } from '../icons/battery.js';
// Beaker aliases
export { default as LucideBeaker } from '../icons/beaker.js';
// BeanOff aliases
export { default as LucideBeanOff } from '../icons/bean-off.js';
// Bean aliases
export { default as LucideBean } from '../icons/bean.js';
// BedDouble aliases
export { default as LucideBedDouble } from '../icons/bed-double.js';
// BedSingle aliases
export { default as LucideBedSingle } from '../icons/bed-single.js';
// Bed aliases
export { default as LucideBed } from '../icons/bed.js';
// Beef aliases
export { default as LucideBeef } from '../icons/beef.js';
// BeerOff aliases
export { default as LucideBeerOff } from '../icons/beer-off.js';
// Beer aliases
export { default as LucideBeer } from '../icons/beer.js';
// BellDot aliases
export { default as LucideBellDot } from '../icons/bell-dot.js';
// BellElectric aliases
export { default as LucideBellElectric } from '../icons/bell-electric.js';
// BellMinus aliases
export { default as LucideBellMinus } from '../icons/bell-minus.js';
// BellOff aliases
export { default as LucideBellOff } from '../icons/bell-off.js';
// BellPlus aliases
export { default as LucideBellPlus } from '../icons/bell-plus.js';
// BellRing aliases
export { default as LucideBellRing } from '../icons/bell-ring.js';
// Bell aliases
export { default as LucideBell } from '../icons/bell.js';
// BetweenVerticalEnd aliases
export { default as LucideBetweenVerticalEnd } from '../icons/between-vertical-end.js';
// BetweenVerticalStart aliases
export { default as LucideBetweenVerticalStart } from '../icons/between-vertical-start.js';
// BicepsFlexed aliases
export { default as LucideBicepsFlexed } from '../icons/biceps-flexed.js';
// Bike aliases
export { default as LucideBike } from '../icons/bike.js';
// Binary aliases
export { default as LucideBinary } from '../icons/binary.js';
// Binoculars aliases
export { default as LucideBinoculars } from '../icons/binoculars.js';
// Biohazard aliases
export { default as LucideBiohazard } from '../icons/biohazard.js';
// Bird aliases
export { default as LucideBird } from '../icons/bird.js';
// Bitcoin aliases
export { default as LucideBitcoin } from '../icons/bitcoin.js';
// Blend aliases
export { default as LucideBlend } from '../icons/blend.js';
// Blocks aliases
export { default as LucideBlocks } from '../icons/blocks.js';
// Blinds aliases
export { default as LucideBlinds } from '../icons/blinds.js';
// BluetoothOff aliases
export { default as LucideBluetoothOff } from '../icons/bluetooth-off.js';
// BluetoothSearching aliases
export { default as LucideBluetoothSearching } from '../icons/bluetooth-searching.js';
// BluetoothConnected aliases
export { default as LucideBluetoothConnected } from '../icons/bluetooth-connected.js';
// Bluetooth aliases
export { default as LucideBluetooth } from '../icons/bluetooth.js';
// Bolt aliases
export { default as LucideBolt } from '../icons/bolt.js';
// Bold aliases
export { default as LucideBold } from '../icons/bold.js';
// Bomb aliases
export { default as LucideBomb } from '../icons/bomb.js';
// Bone aliases
export { default as LucideBone } from '../icons/bone.js';
// BookA aliases
export { default as LucideBookA } from '../icons/book-a.js';
// BookAudio aliases
export { default as LucideBookAudio } from '../icons/book-audio.js';
// BookCheck aliases
export { default as LucideBookCheck } from '../icons/book-check.js';
// BookCopy aliases
export { default as LucideBookCopy } from '../icons/book-copy.js';
// BookDown aliases
export { default as LucideBookDown } from '../icons/book-down.js';
// BookHeadphones aliases
export { default as LucideBookHeadphones } from '../icons/book-headphones.js';
// BookHeart aliases
export { default as LucideBookHeart } from '../icons/book-heart.js';
// BookImage aliases
export { default as LucideBookImage } from '../icons/book-image.js';
// BookKey aliases
export { default as LucideBookKey } from '../icons/book-key.js';
// BookLock aliases
export { default as LucideBookLock } from '../icons/book-lock.js';
// BookMarked aliases
export { default as LucideBookMarked } from '../icons/book-marked.js';
// BookMinus aliases
export { default as LucideBookMinus } from '../icons/book-minus.js';
// BookOpenCheck aliases
export { default as LucideBookOpenCheck } from '../icons/book-open-check.js';
// BookOpenText aliases
export { default as LucideBookOpenText } from '../icons/book-open-text.js';
// BookOpen aliases
export { default as LucideBookOpen } from '../icons/book-open.js';
// BookPlus aliases
export { default as LucideBookPlus } from '../icons/book-plus.js';
// BookText aliases
export { default as LucideBookText } from '../icons/book-text.js';
// BookType aliases
export { default as LucideBookType } from '../icons/book-type.js';
// BookUp2 aliases
export { default as LucideBookUp2 } from '../icons/book-up-2.js';
// BookUp aliases
export { default as LucideBookUp } from '../icons/book-up.js';
// BookUser aliases
export { default as LucideBookUser } from '../icons/book-user.js';
// BookX aliases
export { default as LucideBookX } from '../icons/book-x.js';
// Book aliases
export { default as LucideBook } from '../icons/book.js';
// BookmarkMinus aliases
export { default as LucideBookmarkMinus } from '../icons/bookmark-minus.js';
// BookmarkCheck aliases
export { default as LucideBookmarkCheck } from '../icons/bookmark-check.js';
// BookmarkPlus aliases
export { default as LucideBookmarkPlus } from '../icons/bookmark-plus.js';
// BookmarkX aliases
export { default as LucideBookmarkX } from '../icons/bookmark-x.js';
// Bookmark aliases
export { default as LucideBookmark } from '../icons/bookmark.js';
// BoomBox aliases
export { default as LucideBoomBox } from '../icons/boom-box.js';
// BotMessageSquare aliases
export { default as LucideBotMessageSquare } from '../icons/bot-message-square.js';
// Box aliases
export { default as LucideBox } from '../icons/box.js';
// BotOff aliases
export { default as LucideBotOff } from '../icons/bot-off.js';
// Bot aliases
export { default as LucideBot } from '../icons/bot.js';
// Boxes aliases
export { default as LucideBoxes } from '../icons/boxes.js';
// Brackets aliases
export { default as LucideBrackets } from '../icons/brackets.js';
// BrainCircuit aliases
export { default as LucideBrainCircuit } from '../icons/brain-circuit.js';
// BrainCog aliases
export { default as LucideBrainCog } from '../icons/brain-cog.js';
// Brain aliases
export { default as LucideBrain } from '../icons/brain.js';
// BrickWall aliases
export { default as LucideBrickWall } from '../icons/brick-wall.js';
// BriefcaseBusiness aliases
export { default as LucideBriefcaseBusiness } from '../icons/briefcase-business.js';
// BriefcaseConveyorBelt aliases
export { default as LucideBriefcaseConveyorBelt } from '../icons/briefcase-conveyor-belt.js';
// BriefcaseMedical aliases
export { default as LucideBriefcaseMedical } from '../icons/briefcase-medical.js';
// Briefcase aliases
export { default as LucideBriefcase } from '../icons/briefcase.js';
// BringToFront aliases
export { default as LucideBringToFront } from '../icons/bring-to-front.js';
// Brush aliases
export { default as LucideBrush } from '../icons/brush.js';
// BugOff aliases
export { default as LucideBugOff } from '../icons/bug-off.js';
// BugPlay aliases
export { default as LucideBugPlay } from '../icons/bug-play.js';
// Bug aliases
export { default as LucideBug } from '../icons/bug.js';
// Building2 aliases
export { default as LucideBuilding2 } from '../icons/building-2.js';
// Building aliases
export { default as LucideBuilding } from '../icons/building.js';
// BusFront aliases
export { default as LucideBusFront } from '../icons/bus-front.js';
// Bus aliases
export { default as LucideBus } from '../icons/bus.js';
// Cable aliases
export { default as LucideCable } from '../icons/cable.js';
// CakeSlice aliases
export { default as LucideCakeSlice } from '../icons/cake-slice.js';
// CableCar aliases
export { default as LucideCableCar } from '../icons/cable-car.js';
// Cake aliases
export { default as LucideCake } from '../icons/cake.js';
// Calculator aliases
export { default as LucideCalculator } from '../icons/calculator.js';
// Calendar1 aliases
export { default as LucideCalendar1 } from '../icons/calendar-1.js';
// CalendarArrowDown aliases
export { default as LucideCalendarArrowDown } from '../icons/calendar-arrow-down.js';
// CalendarArrowUp aliases
export { default as LucideCalendarArrowUp } from '../icons/calendar-arrow-up.js';
// CalendarCheck2 aliases
export { default as LucideCalendarCheck2 } from '../icons/calendar-check-2.js';
// CalendarCheck aliases
export { default as LucideCalendarCheck } from '../icons/calendar-check.js';
// CalendarClock aliases
export { default as LucideCalendarClock } from '../icons/calendar-clock.js';
// CalendarCog aliases
export { default as LucideCalendarCog } from '../icons/calendar-cog.js';
// CalendarDays aliases
export { default as LucideCalendarDays } from '../icons/calendar-days.js';
// CalendarHeart aliases
export { default as LucideCalendarHeart } from '../icons/calendar-heart.js';
// CalendarFold aliases
export { default as LucideCalendarFold } from '../icons/calendar-fold.js';
// CalendarMinus2 aliases
export { default as LucideCalendarMinus2 } from '../icons/calendar-minus-2.js';
// CalendarMinus aliases
export { default as LucideCalendarMinus } from '../icons/calendar-minus.js';
// CalendarOff aliases
export { default as LucideCalendarOff } from '../icons/calendar-off.js';
// CalendarPlus2 aliases
export { default as LucideCalendarPlus2 } from '../icons/calendar-plus-2.js';
// CalendarPlus aliases
export { default as LucideCalendarPlus } from '../icons/calendar-plus.js';
// CalendarRange aliases
export { default as LucideCalendarRange } from '../icons/calendar-range.js';
// CalendarSync aliases
export { default as LucideCalendarSync } from '../icons/calendar-sync.js';
// CalendarSearch aliases
export { default as LucideCalendarSearch } from '../icons/calendar-search.js';
// CalendarX2 aliases
export { default as LucideCalendarX2 } from '../icons/calendar-x-2.js';
// CalendarX aliases
export { default as LucideCalendarX } from '../icons/calendar-x.js';
// Calendar aliases
export { default as LucideCalendar } from '../icons/calendar.js';
// CameraOff aliases
export { default as LucideCameraOff } from '../icons/camera-off.js';
// Camera aliases
export { default as LucideCamera } from '../icons/camera.js';
// CandyCane aliases
export { default as LucideCandyCane } from '../icons/candy-cane.js';
// CandyOff aliases
export { default as LucideCandyOff } from '../icons/candy-off.js';
// Candy aliases
export { default as LucideCandy } from '../icons/candy.js';
// Cannabis aliases
export { default as LucideCannabis } from '../icons/cannabis.js';
// CaptionsOff aliases
export { default as LucideCaptionsOff } from '../icons/captions-off.js';
// CarFront aliases
export { default as LucideCarFront } from '../icons/car-front.js';
// CarTaxiFront aliases
export { default as LucideCarTaxiFront } from '../icons/car-taxi-front.js';
// Car aliases
export { default as LucideCar } from '../icons/car.js';
// Caravan aliases
export { default as LucideCaravan } from '../icons/caravan.js';
// Carrot aliases
export { default as LucideCarrot } from '../icons/carrot.js';
// CaseLower aliases
export { default as LucideCaseLower } from '../icons/case-lower.js';
// CaseSensitive aliases
export { default as LucideCaseSensitive } from '../icons/case-sensitive.js';
// CaseUpper aliases
export { default as LucideCaseUpper } from '../icons/case-upper.js';
// CassetteTape aliases
export { default as LucideCassetteTape } from '../icons/cassette-tape.js';
// Cast aliases
export { default as LucideCast } from '../icons/cast.js';
// Castle aliases
export { default as LucideCastle } from '../icons/castle.js';
// Cat aliases
export { default as LucideCat } from '../icons/cat.js';
// Cctv aliases
export { default as LucideCctv } from '../icons/cctv.js';
// ChartBarDecreasing aliases
export { default as LucideChartBarDecreasing } from '../icons/chart-bar-decreasing.js';
// ChartBarIncreasing aliases
export { default as LucideChartBarIncreasing } from '../icons/chart-bar-increasing.js';
// ChartBarStacked aliases
export { default as LucideChartBarStacked } from '../icons/chart-bar-stacked.js';
// ChartColumnDecreasing aliases
export { default as LucideChartColumnDecreasing } from '../icons/chart-column-decreasing.js';
// ChartColumnStacked aliases
export { default as LucideChartColumnStacked } from '../icons/chart-column-stacked.js';
// ChartGantt aliases
export { default as LucideChartGantt } from '../icons/chart-gantt.js';
// ChartNetwork aliases
export { default as LucideChartNetwork } from '../icons/chart-network.js';
// ChartNoAxesColumnDecreasing aliases
export { default as LucideChartNoAxesColumnDecreasing } from '../icons/chart-no-axes-column-decreasing.js';
// ChartNoAxesCombined aliases
export { default as LucideChartNoAxesCombined } from '../icons/chart-no-axes-combined.js';
// ChartSpline aliases
export { default as LucideChartSpline } from '../icons/chart-spline.js';
// CheckCheck aliases
export { default as LucideCheckCheck } from '../icons/check-check.js';
// Check aliases
export { default as LucideCheck } from '../icons/check.js';
// ChefHat aliases
export { default as LucideChefHat } from '../icons/chef-hat.js';
// Cherry aliases
export { default as LucideCherry } from '../icons/cherry.js';
// ChevronDown aliases
export { default as LucideChevronDown } from '../icons/chevron-down.js';
// ChevronFirst aliases
export { default as LucideChevronFirst } from '../icons/chevron-first.js';
// ChevronLast aliases
export { default as LucideChevronLast } from '../icons/chevron-last.js';
// ChevronLeft aliases
export { default as LucideChevronLeft } from '../icons/chevron-left.js';
// ChevronRight aliases
export { default as LucideChevronRight } from '../icons/chevron-right.js';
// ChevronUp aliases
export { default as LucideChevronUp } from '../icons/chevron-up.js';
// ChevronsDownUp aliases
export { default as LucideChevronsDownUp } from '../icons/chevrons-down-up.js';
// ChevronsDown aliases
export { default as LucideChevronsDown } from '../icons/chevrons-down.js';
// ChevronsLeftRightEllipsis aliases
export { default as LucideChevronsLeftRightEllipsis } from '../icons/chevrons-left-right-ellipsis.js';
// ChevronsLeftRight aliases
export { default as LucideChevronsLeftRight } from '../icons/chevrons-left-right.js';
// ChevronsLeft aliases
export { default as LucideChevronsLeft } from '../icons/chevrons-left.js';
// ChevronsRight aliases
export { default as LucideChevronsRight } from '../icons/chevrons-right.js';
// ChevronsRightLeft aliases
export { default as LucideChevronsRightLeft } from '../icons/chevrons-right-left.js';
// ChevronsUpDown aliases
export { default as LucideChevronsUpDown } from '../icons/chevrons-up-down.js';
// ChevronsUp aliases
export { default as LucideChevronsUp } from '../icons/chevrons-up.js';
// Chrome aliases
export { default as LucideChrome } from '../icons/chrome.js';
// Church aliases
export { default as LucideChurch } from '../icons/church.js';
// CigaretteOff aliases
export { default as LucideCigaretteOff } from '../icons/cigarette-off.js';
// Cigarette aliases
export { default as LucideCigarette } from '../icons/cigarette.js';
// CircleDashed aliases
export { default as LucideCircleDashed } from '../icons/circle-dashed.js';
// CircleDotDashed aliases
export { default as LucideCircleDotDashed } from '../icons/circle-dot-dashed.js';
// CircleDollarSign aliases
export { default as LucideCircleDollarSign } from '../icons/circle-dollar-sign.js';
// CircleDot aliases
export { default as LucideCircleDot } from '../icons/circle-dot.js';
// CircleEllipsis aliases
export { default as LucideCircleEllipsis } from '../icons/circle-ellipsis.js';
// CircleFadingArrowUp aliases
export { default as LucideCircleFadingArrowUp } from '../icons/circle-fading-arrow-up.js';
// CircleEqual aliases
export { default as LucideCircleEqual } from '../icons/circle-equal.js';
// CircleFadingPlus aliases
export { default as LucideCircleFadingPlus } from '../icons/circle-fading-plus.js';
// CircleOff aliases
export { default as LucideCircleOff } from '../icons/circle-off.js';
// CircleSlash aliases
export { default as LucideCircleSlash } from '../icons/circle-slash.js';
// CircleSmall aliases
export { default as LucideCircleSmall } from '../icons/circle-small.js';
// Circle aliases
export { default as LucideCircle } from '../icons/circle.js';
// CircuitBoard aliases
export { default as LucideCircuitBoard } from '../icons/circuit-board.js';
// Clapperboard aliases
export { default as LucideClapperboard } from '../icons/clapperboard.js';
// Citrus aliases
export { default as LucideCitrus } from '../icons/citrus.js';
// ClipboardCheck aliases
export { default as LucideClipboardCheck } from '../icons/clipboard-check.js';
// ClipboardCopy aliases
export { default as LucideClipboardCopy } from '../icons/clipboard-copy.js';
// ClipboardList aliases
export { default as LucideClipboardList } from '../icons/clipboard-list.js';
// ClipboardMinus aliases
export { default as LucideClipboardMinus } from '../icons/clipboard-minus.js';
// ClipboardPaste aliases
export { default as LucideClipboardPaste } from '../icons/clipboard-paste.js';
// ClipboardPlus aliases
export { default as LucideClipboardPlus } from '../icons/clipboard-plus.js';
// ClipboardType aliases
export { default as LucideClipboardType } from '../icons/clipboard-type.js';
// ClipboardX aliases
export { default as LucideClipboardX } from '../icons/clipboard-x.js';
// Clipboard aliases
export { default as LucideClipboard } from '../icons/clipboard.js';
// Clock1 aliases
export { default as LucideClock1 } from '../icons/clock-1.js';
// Clock10 aliases
export { default as LucideClock10 } from '../icons/clock-10.js';
// Clock11 aliases
export { default as LucideClock11 } from '../icons/clock-11.js';
// Clock12 aliases
export { default as LucideClock12 } from '../icons/clock-12.js';
// Clock2 aliases
export { default as LucideClock2 } from '../icons/clock-2.js';
// Clock3 aliases
export { default as LucideClock3 } from '../icons/clock-3.js';
// Clock4 aliases
export { default as LucideClock4 } from '../icons/clock-4.js';
// Clock5 aliases
export { default as LucideClock5 } from '../icons/clock-5.js';
// Clock6 aliases
export { default as LucideClock6 } from '../icons/clock-6.js';
// Clock7 aliases
export { default as LucideClock7 } from '../icons/clock-7.js';
// Clock8 aliases
export { default as LucideClock8 } from '../icons/clock-8.js';
// Clock9 aliases
export { default as LucideClock9 } from '../icons/clock-9.js';
// ClockAlert aliases
export { default as LucideClockAlert } from '../icons/clock-alert.js';
// ClockArrowDown aliases
export { default as LucideClockArrowDown } from '../icons/clock-arrow-down.js';
// ClockArrowUp aliases
export { default as LucideClockArrowUp } from '../icons/clock-arrow-up.js';
// ClockFading aliases
export { default as LucideClockFading } from '../icons/clock-fading.js';
// Clock aliases
export { default as LucideClock } from '../icons/clock.js';
// CloudAlert aliases
export { default as LucideCloudAlert } from '../icons/cloud-alert.js';
// CloudCog aliases
export { default as LucideCloudCog } from '../icons/cloud-cog.js';
// CloudDrizzle aliases
export { default as LucideCloudDrizzle } from '../icons/cloud-drizzle.js';
// CloudHail aliases
export { default as LucideCloudHail } from '../icons/cloud-hail.js';
// CloudFog aliases
export { default as LucideCloudFog } from '../icons/cloud-fog.js';
// CloudLightning aliases
export { default as LucideCloudLightning } from '../icons/cloud-lightning.js';
// CloudMoonRain aliases
export { default as LucideCloudMoonRain } from '../icons/cloud-moon-rain.js';
// CloudMoon aliases
export { default as LucideCloudMoon } from '../icons/cloud-moon.js';
// CloudOff aliases
export { default as LucideCloudOff } from '../icons/cloud-off.js';
// CloudRainWind aliases
export { default as LucideCloudRainWind } from '../icons/cloud-rain-wind.js';
// CloudRain aliases
export { default as LucideCloudRain } from '../icons/cloud-rain.js';
// CloudSnow aliases
export { default as LucideCloudSnow } from '../icons/cloud-snow.js';
// CloudSunRain aliases
export { default as LucideCloudSunRain } from '../icons/cloud-sun-rain.js';
// CloudSun aliases
export { default as LucideCloudSun } from '../icons/cloud-sun.js';
// Cloud aliases
export { default as LucideCloud } from '../icons/cloud.js';
// Cloudy aliases
export { default as LucideCloudy } from '../icons/cloudy.js';
// Clover aliases
export { default as LucideClover } from '../icons/clover.js';
// Club aliases
export { default as LucideClub } from '../icons/club.js';
// Code aliases
export { default as LucideCode } from '../icons/code.js';
// Codepen aliases
export { default as LucideCodepen } from '../icons/codepen.js';
// Codesandbox aliases
export { default as LucideCodesandbox } from '../icons/codesandbox.js';
// Coffee aliases
export { default as LucideCoffee } from '../icons/coffee.js';
// Cog aliases
export { default as LucideCog } from '../icons/cog.js';
// Coins aliases
export { default as LucideCoins } from '../icons/coins.js';
// Columns4 aliases
export { default as LucideColumns4 } from '../icons/columns-4.js';
// Combine aliases
export { default as LucideCombine } from '../icons/combine.js';
// Command aliases
export { default as LucideCommand } from '../icons/command.js';
// Compass aliases
export { default as LucideCompass } from '../icons/compass.js';
// Component aliases
export { default as LucideComponent } from '../icons/component.js';
// Computer aliases
export { default as LucideComputer } from '../icons/computer.js';
// ConciergeBell aliases
export { default as LucideConciergeBell } from '../icons/concierge-bell.js';
// Cone aliases
export { default as LucideCone } from '../icons/cone.js';
// Construction aliases
export { default as LucideConstruction } from '../icons/construction.js';
// Container aliases
export { default as LucideContainer } from '../icons/container.js';
// Contact aliases
export { default as LucideContact } from '../icons/contact.js';
// Cookie aliases
export { default as LucideCookie } from '../icons/cookie.js';
// Contrast aliases
export { default as LucideContrast } from '../icons/contrast.js';
// CookingPot aliases
export { default as LucideCookingPot } from '../icons/cooking-pot.js';
// CopyCheck aliases
export { default as LucideCopyCheck } from '../icons/copy-check.js';
// CopyMinus aliases
export { default as LucideCopyMinus } from '../icons/copy-minus.js';
// CopyPlus aliases
export { default as LucideCopyPlus } from '../icons/copy-plus.js';
// CopySlash aliases
export { default as LucideCopySlash } from '../icons/copy-slash.js';
// CopyX aliases
export { default as LucideCopyX } from '../icons/copy-x.js';
// Copy aliases
export { default as LucideCopy } from '../icons/copy.js';
// Copyleft aliases
export { default as LucideCopyleft } from '../icons/copyleft.js';
// Copyright aliases
export { default as LucideCopyright } from '../icons/copyright.js';
// CornerDownLeft aliases
export { default as LucideCornerDownLeft } from '../icons/corner-down-left.js';
// CornerDownRight aliases
export { default as LucideCornerDownRight } from '../icons/corner-down-right.js';
// CornerLeftDown aliases
export { default as LucideCornerLeftDown } from '../icons/corner-left-down.js';
// CornerLeftUp aliases
export { default as LucideCornerLeftUp } from '../icons/corner-left-up.js';
// CornerRightUp aliases
export { default as LucideCornerRightUp } from '../icons/corner-right-up.js';
// CornerRightDown aliases
export { default as LucideCornerRightDown } from '../icons/corner-right-down.js';
// CornerUpRight aliases
export { default as LucideCornerUpRight } from '../icons/corner-up-right.js';
// CornerUpLeft aliases
export { default as LucideCornerUpLeft } from '../icons/corner-up-left.js';
// Cpu aliases
export { default as LucideCpu } from '../icons/cpu.js';
// CreativeCommons aliases
export { default as LucideCreativeCommons } from '../icons/creative-commons.js';
// CreditCard aliases
export { default as LucideCreditCard } from '../icons/credit-card.js';
// Croissant aliases
export { default as LucideCroissant } from '../icons/croissant.js';
// Crop aliases
export { default as LucideCrop } from '../icons/crop.js';
// Crosshair aliases
export { default as LucideCrosshair } from '../icons/crosshair.js';
// Cross aliases
export { default as LucideCross } from '../icons/cross.js';
// Crown aliases
export { default as LucideCrown } from '../icons/crown.js';
// Cuboid aliases
export { default as LucideCuboid } from '../icons/cuboid.js';
// CupSoda aliases
export { default as LucideCupSoda } from '../icons/cup-soda.js';
// Currency aliases
export { default as LucideCurrency } from '../icons/currency.js';
// Cylinder aliases
export { default as LucideCylinder } from '../icons/cylinder.js';
// Dam aliases
export { default as LucideDam } from '../icons/dam.js';
// DatabaseZap aliases
export { default as LucideDatabaseZap } from '../icons/database-zap.js';
// Database aliases
export { default as LucideDatabase } from '../icons/database.js';
// DatabaseBackup aliases
export { default as LucideDatabaseBackup } from '../icons/database-backup.js';
// Delete aliases
export { default as LucideDelete } from '../icons/delete.js';
// Dessert aliases
export { default as LucideDessert } from '../icons/dessert.js';
// Diameter aliases
export { default as LucideDiameter } from '../icons/diameter.js';
// DiamondMinus aliases
export { default as LucideDiamondMinus } from '../icons/diamond-minus.js';
// DiamondPlus aliases
export { default as LucideDiamondPlus } from '../icons/diamond-plus.js';
// Dice1 aliases
export { default as LucideDice1 } from '../icons/dice-1.js';
// Diamond aliases
export { default as LucideDiamond } from '../icons/diamond.js';
// Dice2 aliases
export { default as LucideDice2 } from '../icons/dice-2.js';
// Dice3 aliases
export { default as LucideDice3 } from '../icons/dice-3.js';
// Dice4 aliases
export { default as LucideDice4 } from '../icons/dice-4.js';
// Dice5 aliases
export { default as LucideDice5 } from '../icons/dice-5.js';
// Dice6 aliases
export { default as LucideDice6 } from '../icons/dice-6.js';
// Dices aliases
export { default as LucideDices } from '../icons/dices.js';
// Diff aliases
export { default as LucideDiff } from '../icons/diff.js';
// Disc2 aliases
export { default as LucideDisc2 } from '../icons/disc-2.js';
// Disc3 aliases
export { default as LucideDisc3 } from '../icons/disc-3.js';
// DiscAlbum aliases
export { default as LucideDiscAlbum } from '../icons/disc-album.js';
// Disc aliases
export { default as LucideDisc } from '../icons/disc.js';
// Divide aliases
export { default as LucideDivide } from '../icons/divide.js';
// DnaOff aliases
export { default as LucideDnaOff } from '../icons/dna-off.js';
// Dna aliases
export { default as LucideDna } from '../icons/dna.js';
// Dock aliases
export { default as LucideDock } from '../icons/dock.js';
// Dog aliases
export { default as LucideDog } from '../icons/dog.js';
// Donut aliases
export { default as LucideDonut } from '../icons/donut.js';
// DollarSign aliases
export { default as LucideDollarSign } from '../icons/dollar-sign.js';
// DoorClosed aliases
export { default as LucideDoorClosed } from '../icons/door-closed.js';
// DoorOpen aliases
export { default as LucideDoorOpen } from '../icons/door-open.js';
// Dot aliases
export { default as LucideDot } from '../icons/dot.js';
// Download aliases
export { default as LucideDownload } from '../icons/download.js';
// DraftingCompass aliases
export { default as LucideDraftingCompass } from '../icons/drafting-compass.js';
// Drama aliases
export { default as LucideDrama } from '../icons/drama.js';
// Dribbble aliases
export { default as LucideDribbble } from '../icons/dribbble.js';
// Drill aliases
export { default as LucideDrill } from '../icons/drill.js';
// DropletOff aliases
export { default as LucideDropletOff } from '../icons/droplet-off.js';
// Droplet aliases
export { default as LucideDroplet } from '../icons/droplet.js';
// Droplets aliases
export { default as LucideDroplets } from '../icons/droplets.js';
// Drum aliases
export { default as LucideDrum } from '../icons/drum.js';
// Drumstick aliases
export { default as LucideDrumstick } from '../icons/drumstick.js';
// Dumbbell aliases
export { default as LucideDumbbell } from '../icons/dumbbell.js';
// EarOff aliases
export { default as LucideEarOff } from '../icons/ear-off.js';
// Ear aliases
export { default as LucideEar } from '../icons/ear.js';
// EarthLock aliases
export { default as LucideEarthLock } from '../icons/earth-lock.js';
// Eclipse aliases
export { default as LucideEclipse } from '../icons/eclipse.js';
// EggFried aliases
export { default as LucideEggFried } from '../icons/egg-fried.js';
// EggOff aliases
export { default as LucideEggOff } from '../icons/egg-off.js';
// Egg aliases
export { default as LucideEgg } from '../icons/egg.js';
// EqualApproximately aliases
export { default as LucideEqualApproximately } from '../icons/equal-approximately.js';
// EqualNot aliases
export { default as LucideEqualNot } from '../icons/equal-not.js';
// Equal aliases
export { default as LucideEqual } from '../icons/equal.js';
// Eraser aliases
export { default as LucideEraser } from '../icons/eraser.js';
// EthernetPort aliases
export { default as LucideEthernetPort } from '../icons/ethernet-port.js';
// Euro aliases
export { default as LucideEuro } from '../icons/euro.js';
// Expand aliases
export { default as LucideExpand } from '../icons/expand.js';
// ExternalLink aliases
export { default as LucideExternalLink } from '../icons/external-link.js';
// EyeClosed aliases
export { default as LucideEyeClosed } from '../icons/eye-closed.js';
// EyeOff aliases
export { default as LucideEyeOff } from '../icons/eye-off.js';
// Eye aliases
export { default as LucideEye } from '../icons/eye.js';
// Facebook aliases
export { default as LucideFacebook } from '../icons/facebook.js';
// Factory aliases
export { default as LucideFactory } from '../icons/factory.js';
// Fan aliases
export { default as LucideFan } from '../icons/fan.js';
// FastForward aliases
export { default as LucideFastForward } from '../icons/fast-forward.js';
// Feather aliases
export { default as LucideFeather } from '../icons/feather.js';
// Fence aliases
export { default as LucideFence } from '../icons/fence.js';
// FerrisWheel aliases
export { default as LucideFerrisWheel } from '../icons/ferris-wheel.js';
// Figma aliases
export { default as LucideFigma } from '../icons/figma.js';
// FileArchive aliases
export { default as LucideFileArchive } from '../icons/file-archive.js';
// FileAudio2 aliases
export { default as LucideFileAudio2 } from '../icons/file-audio-2.js';
// FileAudio aliases
export { default as LucideFileAudio } from '../icons/file-audio.js';
// FileBadge2 aliases
export { default as LucideFileBadge2 } from '../icons/file-badge-2.js';
// FileBadge aliases
export { default as LucideFileBadge } from '../icons/file-badge.js';
// FileBox aliases
export { default as LucideFileBox } from '../icons/file-box.js';
// FileCheck2 aliases
export { default as LucideFileCheck2 } from '../icons/file-check-2.js';
// FileCheck aliases
export { default as LucideFileCheck } from '../icons/file-check.js';
// FileClock aliases
export { default as LucideFileClock } from '../icons/file-clock.js';
// FileCode2 aliases
export { default as LucideFileCode2 } from '../icons/file-code-2.js';
// FileCode aliases
export { default as LucideFileCode } from '../icons/file-code.js';
// FileDiff aliases
export { default as LucideFileDiff } from '../icons/file-diff.js';
// FileDigit aliases
export { default as LucideFileDigit } from '../icons/file-digit.js';
// FileDown aliases
export { default as LucideFileDown } from '../icons/file-down.js';
// FileHeart aliases
export { default as LucideFileHeart } from '../icons/file-heart.js';
// FileImage aliases
export { default as LucideFileImage } from '../icons/file-image.js';
// FileInput aliases
export { default as LucideFileInput } from '../icons/file-input.js';
// FileJson2 aliases
export { default as LucideFileJson2 } from '../icons/file-json-2.js';
// FileJson aliases
export { default as LucideFileJson } from '../icons/file-json.js';
// FileKey2 aliases
export { default as LucideFileKey2 } from '../icons/file-key-2.js';
// FileKey aliases
export { default as LucideFileKey } from '../icons/file-key.js';
// FileLock2 aliases
export { default as LucideFileLock2 } from '../icons/file-lock-2.js';
// FileLock aliases
export { default as LucideFileLock } from '../icons/file-lock.js';
// FileMinus2 aliases
export { default as LucideFileMinus2 } from '../icons/file-minus-2.js';
// FileMinus aliases
export { default as LucideFileMinus } from '../icons/file-minus.js';
// FileMusic aliases
export { default as LucideFileMusic } from '../icons/file-music.js';
// FileOutput aliases
export { default as LucideFileOutput } from '../icons/file-output.js';
// FilePlus2 aliases
export { default as LucideFilePlus2 } from '../icons/file-plus-2.js';
// FilePlus aliases
export { default as LucideFilePlus } from '../icons/file-plus.js';
// FileQuestion aliases
export { default as LucideFileQuestion } from '../icons/file-question.js';
// FileScan aliases
export { default as LucideFileScan } from '../icons/file-scan.js';
// FileSearch2 aliases
export { default as LucideFileSearch2 } from '../icons/file-search-2.js';
// FileSearch aliases
export { default as LucideFileSearch } from '../icons/file-search.js';
// FileSliders aliases
export { default as LucideFileSliders } from '../icons/file-sliders.js';
// FileStack aliases
export { default as LucideFileStack } from '../icons/file-stack.js';
// FileSpreadsheet aliases
export { default as LucideFileSpreadsheet } from '../icons/file-spreadsheet.js';
// FileTerminal aliases
export { default as LucideFileTerminal } from '../icons/file-terminal.js';
// FileSymlink aliases
export { default as LucideFileSymlink } from '../icons/file-symlink.js';
// FileText aliases
export { default as LucideFileText } from '../icons/file-text.js';
// FileType2 aliases
export { default as LucideFileType2 } from '../icons/file-type-2.js';
// FileType aliases
export { default as LucideFileType } from '../icons/file-type.js';
// FileUp aliases
export { default as LucideFileUp } from '../icons/file-up.js';
// FileUser aliases
export { default as LucideFileUser } from '../icons/file-user.js';
// FileVideo2 aliases
export { default as LucideFileVideo2 } from '../icons/file-video-2.js';
// FileVideo aliases
export { default as LucideFileVideo } from '../icons/file-video.js';
// FileVolume2 aliases
export { default as LucideFileVolume2 } from '../icons/file-volume-2.js';
// FileVolume aliases
export { default as LucideFileVolume } from '../icons/file-volume.js';
// FileWarning aliases
export { default as LucideFileWarning } from '../icons/file-warning.js';
// FileX2 aliases
export { default as LucideFileX2 } from '../icons/file-x-2.js';
// File aliases
export { default as LucideFile } from '../icons/file.js';
// Files aliases
export { default as LucideFiles } from '../icons/files.js';
// FileX aliases
export { default as LucideFileX } from '../icons/file-x.js';
// Film aliases
export { default as LucideFilm } from '../icons/film.js';
// FilterX aliases
export { default as LucideFilterX } from '../icons/filter-x.js';
// Filter aliases
export { default as LucideFilter } from '../icons/filter.js';
// Fingerprint aliases
export { default as LucideFingerprint } from '../icons/fingerprint.js';
// FireExtinguisher aliases
export { default as LucideFireExtinguisher } from '../icons/fire-extinguisher.js';
// FishOff aliases
export { default as LucideFishOff } from '../icons/fish-off.js';
// FishSymbol aliases
export { default as LucideFishSymbol } from '../icons/fish-symbol.js';
// Fish aliases
export { default as LucideFish } from '../icons/fish.js';
// FlagTriangleLeft aliases
export { default as LucideFlagTriangleLeft } from '../icons/flag-triangle-left.js';
// FlagOff aliases
export { default as LucideFlagOff } from '../icons/flag-off.js';
// FlagTriangleRight aliases
export { default as LucideFlagTriangleRight } from '../icons/flag-triangle-right.js';
// Flag aliases
export { default as LucideFlag } from '../icons/flag.js';
// FlameKindling aliases
export { default as LucideFlameKindling } from '../icons/flame-kindling.js';
// Flame aliases
export { default as LucideFlame } from '../icons/flame.js';
// FlashlightOff aliases
export { default as LucideFlashlightOff } from '../icons/flashlight-off.js';
// Flashlight aliases
export { default as LucideFlashlight } from '../icons/flashlight.js';
// FlaskConicalOff aliases
export { default as LucideFlaskConicalOff } from '../icons/flask-conical-off.js';
// FlaskConical aliases
export { default as LucideFlaskConical } from '../icons/flask-conical.js';
// FlaskRound aliases
export { default as LucideFlaskRound } from '../icons/flask-round.js';
// FlipHorizontal2 aliases
export { default as LucideFlipHorizontal2 } from '../icons/flip-horizontal-2.js';
// FlipHorizontal aliases
export { default as LucideFlipHorizontal } from '../icons/flip-horizontal.js';
// FlipVertical2 aliases
export { default as LucideFlipVertical2 } from '../icons/flip-vertical-2.js';
// FlipVertical aliases
export { default as LucideFlipVertical } from '../icons/flip-vertical.js';
// Flower2 aliases
export { default as LucideFlower2 } from '../icons/flower-2.js';
// Flower aliases
export { default as LucideFlower } from '../icons/flower.js';
// Focus aliases
export { default as LucideFocus } from '../icons/focus.js';
// FoldHorizontal aliases
export { default as LucideFoldHorizontal } from '../icons/fold-horizontal.js';
// FoldVertical aliases
export { default as LucideFoldVertical } from '../icons/fold-vertical.js';
// FolderArchive aliases
export { default as LucideFolderArchive } from '../icons/folder-archive.js';
// FolderClock aliases
export { default as LucideFolderClock } from '../icons/folder-clock.js';
// FolderCheck aliases
export { default as LucideFolderCheck } from '../icons/folder-check.js';
// FolderClosed aliases
export { default as LucideFolderClosed } from '../icons/folder-closed.js';
// FolderCode aliases
export { default as LucideFolderCode } from '../icons/folder-code.js';
// FolderDot aliases
export { default as LucideFolderDot } from '../icons/folder-dot.js';
// FolderDown aliases
export { default as LucideFolderDown } from '../icons/folder-down.js';
// FolderGit2 aliases
export { default as LucideFolderGit2 } from '../icons/folder-git-2.js';
// FolderGit aliases
export { default as LucideFolderGit } from '../icons/folder-git.js';
// FolderHeart aliases
export { default as LucideFolderHeart } from '../icons/folder-heart.js';
// FolderInput aliases
export { default as LucideFolderInput } from '../icons/folder-input.js';
// FolderKanban aliases
export { default as LucideFolderKanban } from '../icons/folder-kanban.js';
// FolderKey aliases
export { default as LucideFolderKey } from '../icons/folder-key.js';
// FolderLock aliases
export { default as LucideFolderLock } from '../icons/folder-lock.js';
// FolderMinus aliases
export { default as LucideFolderMinus } from '../icons/folder-minus.js';
// FolderOpenDot aliases
export { default as LucideFolderOpenDot } from '../icons/folder-open-dot.js';
// FolderOpen aliases
export { default as LucideFolderOpen } from '../icons/folder-open.js';
// FolderPlus aliases
export { default as LucideFolderPlus } from '../icons/folder-plus.js';
// FolderOutput aliases
export { default as LucideFolderOutput } from '../icons/folder-output.js';
// FolderRoot aliases
export { default as LucideFolderRoot } from '../icons/folder-root.js';
// FolderSearch2 aliases
export { default as LucideFolderSearch2 } from '../icons/folder-search-2.js';
// FolderSearch aliases
export { default as LucideFolderSearch } from '../icons/folder-search.js';
// FolderSymlink aliases
export { default as LucideFolderSymlink } from '../icons/folder-symlink.js';
// FolderSync aliases
export { default as LucideFolderSync } from '../icons/folder-sync.js';
// FolderTree aliases
export { default as LucideFolderTree } from '../icons/folder-tree.js';
// FolderX aliases
export { default as LucideFolderX } from '../icons/folder-x.js';
// FolderUp aliases
export { default as LucideFolderUp } from '../icons/folder-up.js';
// Folder aliases
export { default as LucideFolder } from '../icons/folder.js';
// Folders aliases
export { default as LucideFolders } from '../icons/folders.js';
// Footprints aliases
export { default as LucideFootprints } from '../icons/footprints.js';
// Forklift aliases
export { default as LucideForklift } from '../icons/forklift.js';
// Forward aliases
export { default as LucideForward } from '../icons/forward.js';
// Frame aliases
export { default as LucideFrame } from '../icons/frame.js';
// Framer aliases
export { default as LucideFramer } from '../icons/framer.js';
// Fuel aliases
export { default as LucideFuel } from '../icons/fuel.js';
// Frown aliases
export { default as LucideFrown } from '../icons/frown.js';
// Fullscreen aliases
export { default as LucideFullscreen } from '../icons/fullscreen.js';
// GalleryHorizontalEnd aliases
export { default as LucideGalleryHorizontalEnd } from '../icons/gallery-horizontal-end.js';
// GalleryHorizontal aliases
export { default as LucideGalleryHorizontal } from '../icons/gallery-horizontal.js';
// GalleryThumbnails aliases
export { default as LucideGalleryThumbnails } from '../icons/gallery-thumbnails.js';
// GalleryVerticalEnd aliases
export { default as LucideGalleryVerticalEnd } from '../icons/gallery-vertical-end.js';
// GalleryVertical aliases
export { default as LucideGalleryVertical } from '../icons/gallery-vertical.js';
// Gamepad aliases
export { default as LucideGamepad } from '../icons/gamepad.js';
// Gamepad2 aliases
export { default as LucideGamepad2 } from '../icons/gamepad-2.js';
// Gauge aliases
export { default as LucideGauge } from '../icons/gauge.js';
// Gavel aliases
export { default as LucideGavel } from '../icons/gavel.js';
// Gem aliases
export { default as LucideGem } from '../icons/gem.js';
// Ghost aliases
export { default as LucideGhost } from '../icons/ghost.js';
// Gift aliases
export { default as LucideGift } from '../icons/gift.js';
// GitBranchPlus aliases
export { default as LucideGitBranchPlus } from '../icons/git-branch-plus.js';
// GitBranch aliases
export { default as LucideGitBranch } from '../icons/git-branch.js';
// GitCommitVertical aliases
export { default as LucideGitCommitVertical } from '../icons/git-commit-vertical.js';
// GitCompareArrows aliases
export { default as LucideGitCompareArrows } from '../icons/git-compare-arrows.js';
// GitCompare aliases
export { default as LucideGitCompare } from '../icons/git-compare.js';
// GitFork aliases
export { default as LucideGitFork } from '../icons/git-fork.js';
// GitMerge aliases
export { default as LucideGitMerge } from '../icons/git-merge.js';
// GitGraph aliases
export { default as LucideGitGraph } from '../icons/git-graph.js';
// GitPullRequestArrow aliases
export { default as LucideGitPullRequestArrow } from '../icons/git-pull-request-arrow.js';
// GitPullRequestClosed aliases
export { default as LucideGitPullRequestClosed } from '../icons/git-pull-request-closed.js';
// GitPullRequestCreateArrow aliases
export { default as LucideGitPullRequestCreateArrow } from '../icons/git-pull-request-create-arrow.js';
// GitPullRequestCreate aliases
export { default as LucideGitPullRequestCreate } from '../icons/git-pull-request-create.js';
// GitPullRequestDraft aliases
export { default as LucideGitPullRequestDraft } from '../icons/git-pull-request-draft.js';
// GitPullRequest aliases
export { default as LucideGitPullRequest } from '../icons/git-pull-request.js';
// Github aliases
export { default as LucideGithub } from '../icons/github.js';
// Gitlab aliases
export { default as LucideGitlab } from '../icons/gitlab.js';
// GlassWater aliases
export { default as LucideGlassWater } from '../icons/glass-water.js';
// Glasses aliases
export { default as LucideGlasses } from '../icons/glasses.js';
// GlobeLock aliases
export { default as LucideGlobeLock } from '../icons/globe-lock.js';
// Globe aliases
export { default as LucideGlobe } from '../icons/globe.js';
// Goal aliases
export { default as LucideGoal } from '../icons/goal.js';
// Grab aliases
export { default as LucideGrab } from '../icons/grab.js';
// GraduationCap aliases
export { default as LucideGraduationCap } from '../icons/graduation-cap.js';
// Grape aliases
export { default as LucideGrape } from '../icons/grape.js';
// GripHorizontal aliases
export { default as LucideGripHorizontal } from '../icons/grip-horizontal.js';
// GripVertical aliases
export { default as LucideGripVertical } from '../icons/grip-vertical.js';
// Grip aliases
export { default as LucideGrip } from '../icons/grip.js';
// Group aliases
export { default as LucideGroup } from '../icons/group.js';
// Guitar aliases
export { default as LucideGuitar } from '../icons/guitar.js';
// Ham aliases
export { default as LucideHam } from '../icons/ham.js';
// Hammer aliases
export { default as LucideHammer } from '../icons/hammer.js';
// HandCoins aliases
export { default as LucideHandCoins } from '../icons/hand-coins.js';
// HandHeart aliases
export { default as LucideHandHeart } from '../icons/hand-heart.js';
// HandMetal aliases
export { default as LucideHandMetal } from '../icons/hand-metal.js';
// HandPlatter aliases
export { default as LucideHandPlatter } from '../icons/hand-platter.js';
// Hand aliases
export { default as LucideHand } from '../icons/hand.js';
// Handshake aliases
export { default as LucideHandshake } from '../icons/handshake.js';
// HardDriveDownload aliases
export { default as LucideHardDriveDownload } from '../icons/hard-drive-download.js';
// HardDriveUpload aliases
export { default as LucideHardDriveUpload } from '../icons/hard-drive-upload.js';
// HardDrive aliases
export { default as LucideHardDrive } from '../icons/hard-drive.js';
// HardHat aliases
export { default as LucideHardHat } from '../icons/hard-hat.js';
// Hash aliases
export { default as LucideHash } from '../icons/hash.js';
// Haze aliases
export { default as LucideHaze } from '../icons/haze.js';
// HdmiPort aliases
export { default as LucideHdmiPort } from '../icons/hdmi-port.js';
// Heading1 aliases
export { default as LucideHeading1 } from '../icons/heading-1.js';
// Heading2 aliases
export { default as LucideHeading2 } from '../icons/heading-2.js';
// Heading3 aliases
export { default as LucideHeading3 } from '../icons/heading-3.js';
// Heading4 aliases
export { default as LucideHeading4 } from '../icons/heading-4.js';
// Heading6 aliases
export { default as LucideHeading6 } from '../icons/heading-6.js';
// Heading5 aliases
export { default as LucideHeading5 } from '../icons/heading-5.js';
// Heading aliases
export { default as LucideHeading } from '../icons/heading.js';
// HeadphoneOff aliases
export { default as LucideHeadphoneOff } from '../icons/headphone-off.js';
// Headphones aliases
export { default as LucideHeadphones } from '../icons/headphones.js';
// Headset aliases
export { default as LucideHeadset } from '../icons/headset.js';
// HeartCrack aliases
export { default as LucideHeartCrack } from '../icons/heart-crack.js';
// HeartHandshake aliases
export { default as LucideHeartHandshake } from '../icons/heart-handshake.js';
// HeartOff aliases
export { default as LucideHeartOff } from '../icons/heart-off.js';
// HeartPulse aliases
export { default as LucideHeartPulse } from '../icons/heart-pulse.js';
// Heart aliases
export { default as LucideHeart } from '../icons/heart.js';
// Hexagon aliases
export { default as LucideHexagon } from '../icons/hexagon.js';
// Heater aliases
export { default as LucideHeater } from '../icons/heater.js';
// HopOff aliases
export { default as LucideHopOff } from '../icons/hop-off.js';
// Highlighter aliases
export { default as LucideHighlighter } from '../icons/highlighter.js';
// Hop aliases
export { default as LucideHop } from '../icons/hop.js';
// History aliases
export { default as LucideHistory } from '../icons/history.js';
// Hospital aliases
export { default as LucideHospital } from '../icons/hospital.js';
// Hotel aliases
export { default as LucideHotel } from '../icons/hotel.js';
// Hourglass aliases
export { default as LucideHourglass } from '../icons/hourglass.js';
// HousePlug aliases
export { default as LucideHousePlug } from '../icons/house-plug.js';
// HousePlus aliases
export { default as LucideHousePlus } from '../icons/house-plus.js';
// HouseWifi aliases
export { default as LucideHouseWifi } from '../icons/house-wifi.js';
// IdCard aliases
export { default as LucideIdCard } from '../icons/id-card.js';
// ImageDown aliases
export { default as LucideImageDown } from '../icons/image-down.js';
// ImageMinus aliases
export { default as LucideImageMinus } from '../icons/image-minus.js';
// ImageOff aliases
export { default as LucideImageOff } from '../icons/image-off.js';
// ImagePlay aliases
export { default as LucideImagePlay } from '../icons/image-play.js';
// ImageUp aliases
export { default as LucideImageUp } from '../icons/image-up.js';
// ImagePlus aliases
export { default as LucideImagePlus } from '../icons/image-plus.js';
// ImageUpscale aliases
export { default as LucideImageUpscale } from '../icons/image-upscale.js';
// Image aliases
export { default as LucideImage } from '../icons/image.js';
// Images aliases
export { default as LucideImages } from '../icons/images.js';
// Import aliases
export { default as LucideImport } from '../icons/import.js';
// Inbox aliases
export { default as LucideInbox } from '../icons/inbox.js';
// IndianRupee aliases
export { default as LucideIndianRupee } from '../icons/indian-rupee.js';
// Infinity aliases
export { default as LucideInfinity } from '../icons/infinity.js';
// Info aliases
export { default as LucideInfo } from '../icons/info.js';
// InspectionPanel aliases
export { default as LucideInspectionPanel } from '../icons/inspection-panel.js';
// Instagram aliases
export { default as LucideInstagram } from '../icons/instagram.js';
// Italic aliases
export { default as LucideItalic } from '../icons/italic.js';
// IterationCcw aliases
export { default as LucideIterationCcw } from '../icons/iteration-ccw.js';
// IterationCw aliases
export { default as LucideIterationCw } from '../icons/iteration-cw.js';
// JapaneseYen aliases
export { default as LucideJapaneseYen } from '../icons/japanese-yen.js';
// Kanban aliases
export { default as LucideKanban } from '../icons/kanban.js';
// Joystick aliases
export { default as LucideJoystick } from '../icons/joystick.js';
// KeyRound aliases
export { default as LucideKeyRound } from '../icons/key-round.js';
// KeySquare aliases
export { default as LucideKeySquare } from '../icons/key-square.js';
// Key aliases
export { default as LucideKey } from '../icons/key.js';
// KeyboardMusic aliases
export { default as LucideKeyboardMusic } from '../icons/keyboard-music.js';
// KeyboardOff aliases
export { default as LucideKeyboardOff } from '../icons/keyboard-off.js';
// Keyboard aliases
export { default as LucideKeyboard } from '../icons/keyboard.js';
// LampCeiling aliases
export { default as LucideLampCeiling } from '../icons/lamp-ceiling.js';
// LampFloor aliases
export { default as LucideLampFloor } from '../icons/lamp-floor.js';
// LampDesk aliases
export { default as LucideLampDesk } from '../icons/lamp-desk.js';
// LampWallUp aliases
export { default as LucideLampWallUp } from '../icons/lamp-wall-up.js';
// LampWallDown aliases
export { default as LucideLampWallDown } from '../icons/lamp-wall-down.js';
// Lamp aliases
export { default as LucideLamp } from '../icons/lamp.js';
// LandPlot aliases
export { default as LucideLandPlot } from '../icons/land-plot.js';
// Landmark aliases
export { default as LucideLandmark } from '../icons/landmark.js';
// Languages aliases
export { default as LucideLanguages } from '../icons/languages.js';
// LaptopMinimalCheck aliases
export { default as LucideLaptopMinimalCheck } from '../icons/laptop-minimal-check.js';
// Laptop aliases
export { default as LucideLaptop } from '../icons/laptop.js';
// LassoSelect aliases
export { default as LucideLassoSelect } from '../icons/lasso-select.js';
// Lasso aliases
export { default as LucideLasso } from '../icons/lasso.js';
// Laugh aliases
export { default as LucideLaugh } from '../icons/laugh.js';
// Layers2 aliases
export { default as LucideLayers2 } from '../icons/layers-2.js';
// LayoutDashboard aliases
export { default as LucideLayoutDashboard } from '../icons/layout-dashboard.js';
// LayoutGrid aliases
export { default as LucideLayoutGrid } from '../icons/layout-grid.js';
// LayoutList aliases
export { default as LucideLayoutList } from '../icons/layout-list.js';
// LayoutPanelLeft aliases
export { default as LucideLayoutPanelLeft } from '../icons/layout-panel-left.js';
// LayoutPanelTop aliases
export { default as LucideLayoutPanelTop } from '../icons/layout-panel-top.js';
// LayoutTemplate aliases
export { default as LucideLayoutTemplate } from '../icons/layout-template.js';
// Leaf aliases
export { default as LucideLeaf } from '../icons/leaf.js';
// LeafyGreen aliases
export { default as LucideLeafyGreen } from '../icons/leafy-green.js';
// Lectern aliases
export { default as LucideLectern } from '../icons/lectern.js';
// LibraryBig aliases
export { default as LucideLibraryBig } from '../icons/library-big.js';
// LetterText aliases
export { default as LucideLetterText } from '../icons/letter-text.js';
// Library aliases
export { default as LucideLibrary } from '../icons/library.js';
// LifeBuoy aliases
export { default as LucideLifeBuoy } from '../icons/life-buoy.js';
// Ligature aliases
export { default as LucideLigature } from '../icons/ligature.js';
// LightbulbOff aliases
export { default as LucideLightbulbOff } from '../icons/lightbulb-off.js';
// Lightbulb aliases
export { default as LucideLightbulb } from '../icons/lightbulb.js';
// Link2Off aliases
export { default as LucideLink2Off } from '../icons/link-2-off.js';
// Link aliases
export { default as LucideLink } from '../icons/link.js';
// Link2 aliases
export { default as LucideLink2 } from '../icons/link-2.js';
// ListCheck aliases
export { default as LucideListCheck } from '../icons/list-check.js';
// Linkedin aliases
export { default as LucideLinkedin } from '../icons/linkedin.js';
// ListChecks aliases
export { default as LucideListChecks } from '../icons/list-checks.js';
// ListCollapse aliases
export { default as LucideListCollapse } from '../icons/list-collapse.js';
// ListEnd aliases
export { default as LucideListEnd } from '../icons/list-end.js';
// ListFilterPlus aliases
export { default as LucideListFilterPlus } from '../icons/list-filter-plus.js';
// ListFilter aliases
export { default as LucideListFilter } from '../icons/list-filter.js';
// ListMinus aliases
export { default as LucideListMinus } from '../icons/list-minus.js';
// ListOrdered aliases
export { default as LucideListOrdered } from '../icons/list-ordered.js';
// ListPlus aliases
export { default as LucideListPlus } from '../icons/list-plus.js';
// ListRestart aliases
export { default as LucideListRestart } from '../icons/list-restart.js';
// ListMusic aliases
export { default as LucideListMusic } from '../icons/list-music.js';
// ListStart aliases
export { default as LucideListStart } from '../icons/list-start.js';
// ListTodo aliases
export { default as LucideListTodo } from '../icons/list-todo.js';
// ListTree aliases
export { default as LucideListTree } from '../icons/list-tree.js';
// ListVideo aliases
export { default as LucideListVideo } from '../icons/list-video.js';
// ListX aliases
export { default as LucideListX } from '../icons/list-x.js';
// List aliases
export { default as LucideList } from '../icons/list.js';
// Loader aliases
export { default as LucideLoader } from '../icons/loader.js';
// LoaderPinwheel aliases
export { default as LucideLoaderPinwheel } from '../icons/loader-pinwheel.js';
// LocateFixed aliases
export { default as LucideLocateFixed } from '../icons/locate-fixed.js';
// LocateOff aliases
export { default as LucideLocateOff } from '../icons/locate-off.js';
// Locate aliases
export { default as LucideLocate } from '../icons/locate.js';
// LockKeyhole aliases
export { default as LucideLockKeyhole } from '../icons/lock-keyhole.js';
// Lock aliases
export { default as LucideLock } from '../icons/lock.js';
// LogIn aliases
export { default as LucideLogIn } from '../icons/log-in.js';
// LogOut aliases
export { default as LucideLogOut } from '../icons/log-out.js';
// Logs aliases
export { default as LucideLogs } from '../icons/logs.js';
// Lollipop aliases
export { default as LucideLollipop } from '../icons/lollipop.js';
// Luggage aliases
export { default as LucideLuggage } from '../icons/luggage.js';
// Magnet aliases
export { default as LucideMagnet } from '../icons/magnet.js';
// MailCheck aliases
export { default as LucideMailCheck } from '../icons/mail-check.js';
// MailMinus aliases
export { default as LucideMailMinus } from '../icons/mail-minus.js';
// MailOpen aliases
export { default as LucideMailOpen } from '../icons/mail-open.js';
// MailPlus aliases
export { default as LucideMailPlus } from '../icons/mail-plus.js';
// MailQuestion aliases
export { default as LucideMailQuestion } from '../icons/mail-question.js';
// MailSearch aliases
export { default as LucideMailSearch } from '../icons/mail-search.js';
// MailWarning aliases
export { default as LucideMailWarning } from '../icons/mail-warning.js';
// MailX aliases
export { default as LucideMailX } from '../icons/mail-x.js';
// Mail aliases
export { default as LucideMail } from '../icons/mail.js';
// Mailbox aliases
export { default as LucideMailbox } from '../icons/mailbox.js';
// Mails aliases
export { default as LucideMails } from '../icons/mails.js';
// MapPinCheckInside aliases
export { default as LucideMapPinCheckInside } from '../icons/map-pin-check-inside.js';
// MapPinCheck aliases
export { default as LucideMapPinCheck } from '../icons/map-pin-check.js';
// MapPinHouse aliases
export { default as LucideMapPinHouse } from '../icons/map-pin-house.js';
// MapPinMinusInside aliases
export { default as LucideMapPinMinusInside } from '../icons/map-pin-minus-inside.js';
// MapPinMinus aliases
export { default as LucideMapPinMinus } from '../icons/map-pin-minus.js';
// MapPinOff aliases
export { default as LucideMapPinOff } from '../icons/map-pin-off.js';
// MapPinPlusInside aliases
export { default as LucideMapPinPlusInside } from '../icons/map-pin-plus-inside.js';
// MapPinPlus aliases
export { default as LucideMapPinPlus } from '../icons/map-pin-plus.js';
// MapPinXInside aliases
export { default as LucideMapPinXInside } from '../icons/map-pin-x-inside.js';
// MapPinX aliases
export { default as LucideMapPinX } from '../icons/map-pin-x.js';
// MapPin aliases
export { default as LucideMapPin } from '../icons/map-pin.js';
// MapPinned aliases
export { default as LucideMapPinned } from '../icons/map-pinned.js';
// MapPlus aliases
export { default as LucideMapPlus } from '../icons/map-plus.js';
// Map aliases
export { default as LucideMap } from '../icons/map.js';
// MarsStroke aliases
export { default as LucideMarsStroke } from '../icons/mars-stroke.js';
// Mars aliases
export { default as LucideMars } from '../icons/mars.js';
// Martini aliases
export { default as LucideMartini } from '../icons/martini.js';
// Maximize2 aliases
export { default as LucideMaximize2 } from '../icons/maximize-2.js';
// Maximize aliases
export { default as LucideMaximize } from '../icons/maximize.js';
// Medal aliases
export { default as LucideMedal } from '../icons/medal.js';
// Megaphone aliases
export { default as LucideMegaphone } from '../icons/megaphone.js';
// MegaphoneOff aliases
export { default as LucideMegaphoneOff } from '../icons/megaphone-off.js';
// Meh aliases
export { default as LucideMeh } from '../icons/meh.js';
// MemoryStick aliases
export { default as LucideMemoryStick } from '../icons/memory-stick.js';
// Menu aliases
export { default as LucideMenu } from '../icons/menu.js';
// Merge aliases
export { default as LucideMerge } from '../icons/merge.js';
// MessageCircleCode aliases
export { default as LucideMessageCircleCode } from '../icons/message-circle-code.js';
// MessageCircleDashed aliases
export { default as LucideMessageCircleDashed } from '../icons/message-circle-dashed.js';
// MessageCircleHeart aliases
export { default as LucideMessageCircleHeart } from '../icons/message-circle-heart.js';
// MessageCircleMore aliases
export { default as LucideMessageCircleMore } from '../icons/message-circle-more.js';
// MessageCircleOff aliases
export { default as LucideMessageCircleOff } from '../icons/message-circle-off.js';
// MessageCirclePlus aliases
export { default as LucideMessageCirclePlus } from '../icons/message-circle-plus.js';
// MessageCircleQuestion aliases
export { default as LucideMessageCircleQuestion } from '../icons/message-circle-question.js';
// MessageCircleReply aliases
export { default as LucideMessageCircleReply } from '../icons/message-circle-reply.js';
// MessageCircleWarning aliases
export { default as LucideMessageCircleWarning } from '../icons/message-circle-warning.js';
// MessageCircleX aliases
export { default as LucideMessageCircleX } from '../icons/message-circle-x.js';
// MessageCircle aliases
export { default as LucideMessageCircle } from '../icons/message-circle.js';
// MessageSquareCode aliases
export { default as LucideMessageSquareCode } from '../icons/message-square-code.js';
// MessageSquareDashed aliases
export { default as LucideMessageSquareDashed } from '../icons/message-square-dashed.js';
// MessageSquareDiff aliases
export { default as LucideMessageSquareDiff } from '../icons/message-square-diff.js';
// MessageSquareDot aliases
export { default as LucideMessageSquareDot } from '../icons/message-square-dot.js';
// MessageSquareHeart aliases
export { default as LucideMessageSquareHeart } from '../icons/message-square-heart.js';
// MessageSquareLock aliases
export { default as LucideMessageSquareLock } from '../icons/message-square-lock.js';
// MessageSquareMore aliases
export { default as LucideMessageSquareMore } from '../icons/message-square-more.js';
// MessageSquareOff aliases
export { default as LucideMessageSquareOff } from '../icons/message-square-off.js';
// MessageSquareQuote aliases
export { default as LucideMessageSquareQuote } from '../icons/message-square-quote.js';
// MessageSquarePlus aliases
export { default as LucideMessageSquarePlus } from '../icons/message-square-plus.js';
// MessageSquareShare aliases
export { default as LucideMessageSquareShare } from '../icons/message-square-share.js';
// MessageSquareReply aliases
export { default as LucideMessageSquareReply } from '../icons/message-square-reply.js';
// MessageSquareText aliases
export { default as LucideMessageSquareText } from '../icons/message-square-text.js';
// MessageSquareWarning aliases
export { default as LucideMessageSquareWarning } from '../icons/message-square-warning.js';
// MessageSquareX aliases
export { default as LucideMessageSquareX } from '../icons/message-square-x.js';
// MessageSquare aliases
export { default as LucideMessageSquare } from '../icons/message-square.js';
// MessagesSquare aliases
export { default as LucideMessagesSquare } from '../icons/messages-square.js';
// MicOff aliases
export { default as LucideMicOff } from '../icons/mic-off.js';
// Mic aliases
export { default as LucideMic } from '../icons/mic.js';
// Microchip aliases
export { default as LucideMicrochip } from '../icons/microchip.js';
// Microscope aliases
export { default as LucideMicroscope } from '../icons/microscope.js';
// Microwave aliases
export { default as LucideMicrowave } from '../icons/microwave.js';
// Milestone aliases
export { default as LucideMilestone } from '../icons/milestone.js';
// Milk aliases
export { default as LucideMilk } from '../icons/milk.js';
// MilkOff aliases
export { default as LucideMilkOff } from '../icons/milk-off.js';
// Minimize2 aliases
export { default as LucideMinimize2 } from '../icons/minimize-2.js';
// Minimize aliases
export { default as LucideMinimize } from '../icons/minimize.js';
// Minus aliases
export { default as LucideMinus } from '../icons/minus.js';
// MonitorCheck aliases
export { default as LucideMonitorCheck } from '../icons/monitor-check.js';
// MonitorCog aliases
export { default as LucideMonitorCog } from '../icons/monitor-cog.js';
// MonitorDot aliases
export { default as LucideMonitorDot } from '../icons/monitor-dot.js';
// MonitorDown aliases
export { default as LucideMonitorDown } from '../icons/monitor-down.js';
// MonitorPause aliases
export { default as LucideMonitorPause } from '../icons/monitor-pause.js';
// MonitorOff aliases
export { default as LucideMonitorOff } from '../icons/monitor-off.js';
// MonitorPlay aliases
export { default as LucideMonitorPlay } from '../icons/monitor-play.js';
// MonitorSmartphone aliases
export { default as LucideMonitorSmartphone } from '../icons/monitor-smartphone.js';
// MonitorSpeaker aliases
export { default as LucideMonitorSpeaker } from '../icons/monitor-speaker.js';
// MonitorStop aliases
export { default as LucideMonitorStop } from '../icons/monitor-stop.js';
// MonitorUp aliases
export { default as LucideMonitorUp } from '../icons/monitor-up.js';
// MonitorX aliases
export { default as LucideMonitorX } from '../icons/monitor-x.js';
// MoonStar aliases
export { default as LucideMoonStar } from '../icons/moon-star.js';
// Monitor aliases
export { default as LucideMonitor } from '../icons/monitor.js';
// Moon aliases
export { default as LucideMoon } from '../icons/moon.js';
// MountainSnow aliases
export { default as LucideMountainSnow } from '../icons/mountain-snow.js';
// Mountain aliases
export { default as LucideMountain } from '../icons/mountain.js';
// MouseOff aliases
export { default as LucideMouseOff } from '../icons/mouse-off.js';
// MousePointer2 aliases
export { default as LucideMousePointer2 } from '../icons/mouse-pointer-2.js';
// MousePointerBan aliases
export { default as LucideMousePointerBan } from '../icons/mouse-pointer-ban.js';
// MousePointer aliases
export { default as LucideMousePointer } from '../icons/mouse-pointer.js';
// MousePointerClick aliases
export { default as LucideMousePointerClick } from '../icons/mouse-pointer-click.js';
// Mouse aliases
export { default as LucideMouse } from '../icons/mouse.js';
// MoveDiagonal2 aliases
export { default as LucideMoveDiagonal2 } from '../icons/move-diagonal-2.js';
// MoveDiagonal aliases
export { default as LucideMoveDiagonal } from '../icons/move-diagonal.js';
// MoveDownLeft aliases
export { default as LucideMoveDownLeft } from '../icons/move-down-left.js';
// MoveDownRight aliases
export { default as LucideMoveDownRight } from '../icons/move-down-right.js';
// MoveDown aliases
export { default as LucideMoveDown } from '../icons/move-down.js';
// MoveHorizontal aliases
export { default as LucideMoveHorizontal } from '../icons/move-horizontal.js';
// MoveLeft aliases
export { default as LucideMoveLeft } from '../icons/move-left.js';
// MoveRight aliases
export { default as LucideMoveRight } from '../icons/move-right.js';
// MoveUpLeft aliases
export { default as LucideMoveUpLeft } from '../icons/move-up-left.js';
// MoveUpRight aliases
export { default as LucideMoveUpRight } from '../icons/move-up-right.js';
// MoveUp aliases
export { default as LucideMoveUp } from '../icons/move-up.js';
// MoveVertical aliases
export { default as LucideMoveVertical } from '../icons/move-vertical.js';
// Move aliases
export { default as LucideMove } from '../icons/move.js';
// Music2 aliases
export { default as LucideMusic2 } from '../icons/music-2.js';
// Music3 aliases
export { default as LucideMusic3 } from '../icons/music-3.js';
// Music4 aliases
export { default as LucideMusic4 } from '../icons/music-4.js';
// Navigation2Off aliases
export { default as LucideNavigation2Off } from '../icons/navigation-2-off.js';
// Music aliases
export { default as LucideMusic } from '../icons/music.js';
// Navigation2 aliases
export { default as LucideNavigation2 } from '../icons/navigation-2.js';
// NavigationOff aliases
export { default as LucideNavigationOff } from '../icons/navigation-off.js';
// Navigation aliases
export { default as LucideNavigation } from '../icons/navigation.js';
// Network aliases
export { default as LucideNetwork } from '../icons/network.js';
// Newspaper aliases
export { default as LucideNewspaper } from '../icons/newspaper.js';
// Nfc aliases
export { default as LucideNfc } from '../icons/nfc.js';
// NonBinary aliases
export { default as LucideNonBinary } from '../icons/non-binary.js';
// NotebookPen aliases
export { default as LucideNotebookPen } from '../icons/notebook-pen.js';
// NotebookText aliases
export { default as LucideNotebookText } from '../icons/notebook-text.js';
// NotebookTabs aliases
export { default as LucideNotebookTabs } from '../icons/notebook-tabs.js';
// Notebook aliases
export { default as LucideNotebook } from '../icons/notebook.js';
// NotepadTextDashed aliases
export { default as LucideNotepadTextDashed } from '../icons/notepad-text-dashed.js';
// NotepadText aliases
export { default as LucideNotepadText } from '../icons/notepad-text.js';
// Nut aliases
export { default as LucideNut } from '../icons/nut.js';
// NutOff aliases
export { default as LucideNutOff } from '../icons/nut-off.js';
// OctagonMinus aliases
export { default as LucideOctagonMinus } from '../icons/octagon-minus.js';
// Omega aliases
export { default as LucideOmega } from '../icons/omega.js';
// Octagon aliases
export { default as LucideOctagon } from '../icons/octagon.js';
// Option aliases
export { default as LucideOption } from '../icons/option.js';
// Orbit aliases
export { default as LucideOrbit } from '../icons/orbit.js';
// Origami aliases
export { default as LucideOrigami } from '../icons/origami.js';
// Package2 aliases
export { default as LucidePackage2 } from '../icons/package-2.js';
// PackageCheck aliases
export { default as LucidePackageCheck } from '../icons/package-check.js';
// PackageMinus aliases
export { default as LucidePackageMinus } from '../icons/package-minus.js';
// PackagePlus aliases
export { default as LucidePackagePlus } from '../icons/package-plus.js';
// PackageOpen aliases
export { default as LucidePackageOpen } from '../icons/package-open.js';
// PackageSearch aliases
export { default as LucidePackageSearch } from '../icons/package-search.js';
// PackageX aliases
export { default as LucidePackageX } from '../icons/package-x.js';
// Package aliases
export { default as LucidePackage } from '../icons/package.js';
// PaintBucket aliases
export { default as LucidePaintBucket } from '../icons/paint-bucket.js';
// PaintRoller aliases
export { default as LucidePaintRoller } from '../icons/paint-roller.js';
// Paintbrush aliases
export { default as LucidePaintbrush } from '../icons/paintbrush.js';
// Palette aliases
export { default as LucidePalette } from '../icons/palette.js';
// PanelBottomClose aliases
export { default as LucidePanelBottomClose } from '../icons/panel-bottom-close.js';
// PanelBottomOpen aliases
export { default as LucidePanelBottomOpen } from '../icons/panel-bottom-open.js';
// PanelBottom aliases
export { default as LucidePanelBottom } from '../icons/panel-bottom.js';
// PanelRightClose aliases
export { default as LucidePanelRightClose } from '../icons/panel-right-close.js';
// PanelRightOpen aliases
export { default as LucidePanelRightOpen } from '../icons/panel-right-open.js';
// PanelRight aliases
export { default as LucidePanelRight } from '../icons/panel-right.js';
// PanelTopClose aliases
export { default as LucidePanelTopClose } from '../icons/panel-top-close.js';
// PanelTopOpen aliases
export { default as LucidePanelTopOpen } from '../icons/panel-top-open.js';
// PanelTop aliases
export { default as LucidePanelTop } from '../icons/panel-top.js';
// PanelsLeftBottom aliases
export { default as LucidePanelsLeftBottom } from '../icons/panels-left-bottom.js';
// PanelsRightBottom aliases
export { default as LucidePanelsRightBottom } from '../icons/panels-right-bottom.js';
// Paperclip aliases
export { default as LucidePaperclip } from '../icons/paperclip.js';
// Parentheses aliases
export { default as LucideParentheses } from '../icons/parentheses.js';
// ParkingMeter aliases
export { default as LucideParkingMeter } from '../icons/parking-meter.js';
// Pause aliases
export { default as LucidePause } from '../icons/pause.js';
// PartyPopper aliases
export { default as LucidePartyPopper } from '../icons/party-popper.js';
// PawPrint aliases
export { default as LucidePawPrint } from '../icons/paw-print.js';
// PcCase aliases
export { default as LucidePcCase } from '../icons/pc-case.js';
// PenOff aliases
export { default as LucidePenOff } from '../icons/pen-off.js';
// PenTool aliases
export { default as LucidePenTool } from '../icons/pen-tool.js';
// PencilLine aliases
export { default as LucidePencilLine } from '../icons/pencil-line.js';
// PencilOff aliases
export { default as LucidePencilOff } from '../icons/pencil-off.js';
// PencilRuler aliases
export { default as LucidePencilRuler } from '../icons/pencil-ruler.js';
// Pencil aliases
export { default as LucidePencil } from '../icons/pencil.js';
// Pentagon aliases
export { default as LucidePentagon } from '../icons/pentagon.js';
// Percent aliases
export { default as LucidePercent } from '../icons/percent.js';
// PersonStanding aliases
export { default as LucidePersonStanding } from '../icons/person-standing.js';
// PhilippinePeso aliases
export { default as LucidePhilippinePeso } from '../icons/philippine-peso.js';
// PhoneCall aliases
export { default as LucidePhoneCall } from '../icons/phone-call.js';
// PhoneIncoming aliases
export { default as LucidePhoneIncoming } from '../icons/phone-incoming.js';
// PhoneForwarded aliases
export { default as LucidePhoneForwarded } from '../icons/phone-forwarded.js';
// PhoneMissed aliases
export { default as LucidePhoneMissed } from '../icons/phone-missed.js';
// PhoneOff aliases
export { default as LucidePhoneOff } from '../icons/phone-off.js';
// PhoneOutgoing aliases
export { default as LucidePhoneOutgoing } from '../icons/phone-outgoing.js';
// Phone aliases
export { default as LucidePhone } from '../icons/phone.js';
// Piano aliases
export { default as LucidePiano } from '../icons/piano.js';
// Pi aliases
export { default as LucidePi } from '../icons/pi.js';
// Pickaxe aliases
export { default as LucidePickaxe } from '../icons/pickaxe.js';
// PictureInPicture2 aliases
export { default as LucidePictureInPicture2 } from '../icons/picture-in-picture-2.js';
// PictureInPicture aliases
export { default as LucidePictureInPicture } from '../icons/picture-in-picture.js';
// PilcrowLeft aliases
export { default as LucidePilcrowLeft } from '../icons/pilcrow-left.js';
// PiggyBank aliases
export { default as LucidePiggyBank } from '../icons/piggy-bank.js';
// PilcrowRight aliases
export { default as LucidePilcrowRight } from '../icons/pilcrow-right.js';
// Pilcrow aliases
export { default as LucidePilcrow } from '../icons/pilcrow.js';
// Pill aliases
export { default as LucidePill } from '../icons/pill.js';
// PinOff aliases
export { default as LucidePinOff } from '../icons/pin-off.js';
// PillBottle aliases
export { default as LucidePillBottle } from '../icons/pill-bottle.js';
// Pin aliases
export { default as LucidePin } from '../icons/pin.js';
// Pipette aliases
export { default as LucidePipette } from '../icons/pipette.js';
// Pizza aliases
export { default as LucidePizza } from '../icons/pizza.js';
// PlaneLanding aliases
export { default as LucidePlaneLanding } from '../icons/plane-landing.js';
// PlaneTakeoff aliases
export { default as LucidePlaneTakeoff } from '../icons/plane-takeoff.js';
// Plane aliases
export { default as LucidePlane } from '../icons/plane.js';
// Play aliases
export { default as LucidePlay } from '../icons/play.js';
// Plug2 aliases
export { default as LucidePlug2 } from '../icons/plug-2.js';
// Plus aliases
export { default as LucidePlus } from '../icons/plus.js';
// Plug aliases
export { default as LucidePlug } from '../icons/plug.js';
// PocketKnife aliases
export { default as LucidePocketKnife } from '../icons/pocket-knife.js';
// Pocket aliases
export { default as LucidePocket } from '../icons/pocket.js';
// Podcast aliases
export { default as LucidePodcast } from '../icons/podcast.js';
// PointerOff aliases
export { default as LucidePointerOff } from '../icons/pointer-off.js';
// Pointer aliases
export { default as LucidePointer } from '../icons/pointer.js';
// Popcorn aliases
export { default as LucidePopcorn } from '../icons/popcorn.js';
// Popsicle aliases
export { default as LucidePopsicle } from '../icons/popsicle.js';
// PoundSterling aliases
export { default as LucidePoundSterling } from '../icons/pound-sterling.js';
// PowerOff aliases
export { default as LucidePowerOff } from '../icons/power-off.js';
// Power aliases
export { default as LucidePower } from '../icons/power.js';
// Presentation aliases
export { default as LucidePresentation } from '../icons/presentation.js';
// PrinterCheck aliases
export { default as LucidePrinterCheck } from '../icons/printer-check.js';
// Printer aliases
export { default as LucidePrinter } from '../icons/printer.js';
// Projector aliases
export { default as LucideProjector } from '../icons/projector.js';
// Proportions aliases
export { default as LucideProportions } from '../icons/proportions.js';
// Puzzle aliases
export { default as LucidePuzzle } from '../icons/puzzle.js';
// Pyramid aliases
export { default as LucidePyramid } from '../icons/pyramid.js';
// QrCode aliases
export { default as LucideQrCode } from '../icons/qr-code.js';
// Rabbit aliases
export { default as LucideRabbit } from '../icons/rabbit.js';
// Quote aliases
export { default as LucideQuote } from '../icons/quote.js';
// Radar aliases
export { default as LucideRadar } from '../icons/radar.js';
// Radiation aliases
export { default as LucideRadiation } from '../icons/radiation.js';
// Radical aliases
export { default as LucideRadical } from '../icons/radical.js';
// RadioTower aliases
export { default as LucideRadioTower } from '../icons/radio-tower.js';
// RadioReceiver aliases
export { default as LucideRadioReceiver } from '../icons/radio-receiver.js';
// Radio aliases
export { default as LucideRadio } from '../icons/radio.js';
// Radius aliases
export { default as LucideRadius } from '../icons/radius.js';
// RailSymbol aliases
export { default as LucideRailSymbol } from '../icons/rail-symbol.js';
// Rainbow aliases
export { default as LucideRainbow } from '../icons/rainbow.js';
// Rat aliases
export { default as LucideRat } from '../icons/rat.js';
// Ratio aliases
export { default as LucideRatio } from '../icons/ratio.js';
// ReceiptCent aliases
export { default as LucideReceiptCent } from '../icons/receipt-cent.js';
// ReceiptEuro aliases
export { default as LucideReceiptEuro } from '../icons/receipt-euro.js';
// ReceiptIndianRupee aliases
export { default as LucideReceiptIndianRupee } from '../icons/receipt-indian-rupee.js';
// ReceiptJapaneseYen aliases
export { default as LucideReceiptJapaneseYen } from '../icons/receipt-japanese-yen.js';
// ReceiptPoundSterling aliases
export { default as LucideReceiptPoundSterling } from '../icons/receipt-pound-sterling.js';
// ReceiptRussianRuble aliases
export { default as LucideReceiptRussianRuble } from '../icons/receipt-russian-ruble.js';
// ReceiptSwissFranc aliases
export { default as LucideReceiptSwissFranc } from '../icons/receipt-swiss-franc.js';
// ReceiptText aliases
export { default as LucideReceiptText } from '../icons/receipt-text.js';
// Receipt aliases
export { default as LucideReceipt } from '../icons/receipt.js';
// RectangleHorizontal aliases
export { default as LucideRectangleHorizontal } from '../icons/rectangle-horizontal.js';
// RectangleVertical aliases
export { default as LucideRectangleVertical } from '../icons/rectangle-vertical.js';
// Recycle aliases
export { default as LucideRecycle } from '../icons/recycle.js';
// Redo2 aliases
export { default as LucideRedo2 } from '../icons/redo-2.js';
// RedoDot aliases
export { default as LucideRedoDot } from '../icons/redo-dot.js';
// Redo aliases
export { default as LucideRedo } from '../icons/redo.js';
// RefreshCcw aliases
export { default as LucideRefreshCcw } from '../icons/refresh-ccw.js';
// RefreshCcwDot aliases
export { default as LucideRefreshCcwDot } from '../icons/refresh-ccw-dot.js';
// RefreshCwOff aliases
export { default as LucideRefreshCwOff } from '../icons/refresh-cw-off.js';
// RefreshCw aliases
export { default as LucideRefreshCw } from '../icons/refresh-cw.js';
// Refrigerator aliases
export { default as LucideRefrigerator } from '../icons/refrigerator.js';
// Regex aliases
export { default as LucideRegex } from '../icons/regex.js';
// RemoveFormatting aliases
export { default as LucideRemoveFormatting } from '../icons/remove-formatting.js';
// Repeat1 aliases
export { default as LucideRepeat1 } from '../icons/repeat-1.js';
// Repeat2 aliases
export { default as LucideRepeat2 } from '../icons/repeat-2.js';
// Repeat aliases
export { default as LucideRepeat } from '../icons/repeat.js';
// ReplaceAll aliases
export { default as LucideReplaceAll } from '../icons/replace-all.js';
// Replace aliases
export { default as LucideReplace } from '../icons/replace.js';
// ReplyAll aliases
export { default as LucideReplyAll } from '../icons/reply-all.js';
// Reply aliases
export { default as LucideReply } from '../icons/reply.js';
// Rewind aliases
export { default as LucideRewind } from '../icons/rewind.js';
// Ribbon aliases
export { default as LucideRibbon } from '../icons/ribbon.js';
// Rocket aliases
export { default as LucideRocket } from '../icons/rocket.js';
// RockingChair aliases
export { default as LucideRockingChair } from '../icons/rocking-chair.js';
// RollerCoaster aliases
export { default as LucideRollerCoaster } from '../icons/roller-coaster.js';
// RotateCcwSquare aliases
export { default as LucideRotateCcwSquare } from '../icons/rotate-ccw-square.js';
// RotateCcw aliases
export { default as LucideRotateCcw } from '../icons/rotate-ccw.js';
// RotateCwSquare aliases
export { default as LucideRotateCwSquare } from '../icons/rotate-cw-square.js';
// RotateCw aliases
export { default as LucideRotateCw } from '../icons/rotate-cw.js';
// Route aliases
export { default as LucideRoute } from '../icons/route.js';
// RouteOff aliases
export { default as LucideRouteOff } from '../icons/route-off.js';
// Router aliases
export { default as LucideRouter } from '../icons/router.js';
// Rows4 aliases
export { default as LucideRows4 } from '../icons/rows-4.js';
// Rss aliases
export { default as LucideRss } from '../icons/rss.js';
// RussianRuble aliases
export { default as LucideRussianRuble } from '../icons/russian-ruble.js';
// Ruler aliases
export { default as LucideRuler } from '../icons/ruler.js';
// Sailboat aliases
export { default as LucideSailboat } from '../icons/sailboat.js';
// Salad aliases
export { default as LucideSalad } from '../icons/salad.js';
// Sandwich aliases
export { default as LucideSandwich } from '../icons/sandwich.js';
// SatelliteDish aliases
export { default as LucideSatelliteDish } from '../icons/satellite-dish.js';
// Satellite aliases
export { default as LucideSatellite } from '../icons/satellite.js';
// SaudiRiyal aliases
export { default as LucideSaudiRiyal } from '../icons/saudi-riyal.js';
// SaveAll aliases
export { default as LucideSaveAll } from '../icons/save-all.js';
// SaveOff aliases
export { default as LucideSaveOff } from '../icons/save-off.js';
// Save aliases
export { default as LucideSave } from '../icons/save.js';
// Scale aliases
export { default as LucideScale } from '../icons/scale.js';
// Scaling aliases
export { default as LucideScaling } from '../icons/scaling.js';
// ScanBarcode aliases
export { default as LucideScanBarcode } from '../icons/scan-barcode.js';
// ScanFace aliases
export { default as LucideScanFace } from '../icons/scan-face.js';
// ScanEye aliases
export { default as LucideScanEye } from '../icons/scan-eye.js';
// ScanHeart aliases
export { default as LucideScanHeart } from '../icons/scan-heart.js';
// ScanLine aliases
export { default as LucideScanLine } from '../icons/scan-line.js';
// ScanQrCode aliases
export { default as LucideScanQrCode } from '../icons/scan-qr-code.js';
// ScanSearch aliases
export { default as LucideScanSearch } from '../icons/scan-search.js';
// ScanText aliases
export { default as LucideScanText } from '../icons/scan-text.js';
// School aliases
export { default as LucideSchool } from '../icons/school.js';
// ScissorsLineDashed aliases
export { default as LucideScissorsLineDashed } from '../icons/scissors-line-dashed.js';
// Scan aliases
export { default as LucideScan } from '../icons/scan.js';
// Scissors aliases
export { default as LucideScissors } from '../icons/scissors.js';
// ScreenShareOff aliases
export { default as LucideScreenShareOff } from '../icons/screen-share-off.js';
// ScreenShare aliases
export { default as LucideScreenShare } from '../icons/screen-share.js';
// ScrollText aliases
export { default as LucideScrollText } from '../icons/scroll-text.js';
// SearchCode aliases
export { default as LucideSearchCode } from '../icons/search-code.js';
// Scroll aliases
export { default as LucideScroll } from '../icons/scroll.js';
// SearchCheck aliases
export { default as LucideSearchCheck } from '../icons/search-check.js';
// SearchSlash aliases
export { default as LucideSearchSlash } from '../icons/search-slash.js';
// SearchX aliases
export { default as LucideSearchX } from '../icons/search-x.js';
// Search aliases
export { default as LucideSearch } from '../icons/search.js';
// SendToBack aliases
export { default as LucideSendToBack } from '../icons/send-to-back.js';
// Section aliases
export { default as LucideSection } from '../icons/section.js';
// Send aliases
export { default as LucideSend } from '../icons/send.js';
// SeparatorHorizontal aliases
export { default as LucideSeparatorHorizontal } from '../icons/separator-horizontal.js';
// SeparatorVertical aliases
export { default as LucideSeparatorVertical } from '../icons/separator-vertical.js';
// ServerCog aliases
export { default as LucideServerCog } from '../icons/server-cog.js';
// ServerCrash aliases
export { default as LucideServerCrash } from '../icons/server-crash.js';
// ServerOff aliases
export { default as LucideServerOff } from '../icons/server-off.js';
// Server aliases
export { default as LucideServer } from '../icons/server.js';
// Settings2 aliases
export { default as LucideSettings2 } from '../icons/settings-2.js';
// Settings aliases
export { default as LucideSettings } from '../icons/settings.js';
// Shapes aliases
export { default as LucideShapes } from '../icons/shapes.js';
// Share2 aliases
export { default as LucideShare2 } from '../icons/share-2.js';
// Share aliases
export { default as LucideShare } from '../icons/share.js';
// Sheet aliases
export { default as LucideSheet } from '../icons/sheet.js';
// Shell aliases
export { default as LucideShell } from '../icons/shell.js';
// ShieldAlert aliases
export { default as LucideShieldAlert } from '../icons/shield-alert.js';
// ShieldBan aliases
export { default as LucideShieldBan } from '../icons/shield-ban.js';
// ShieldCheck aliases
export { default as LucideShieldCheck } from '../icons/shield-check.js';
// ShieldEllipsis aliases
export { default as LucideShieldEllipsis } from '../icons/shield-ellipsis.js';
// ShieldHalf aliases
export { default as LucideShieldHalf } from '../icons/shield-half.js';
// ShieldMinus aliases
export { default as LucideShieldMinus } from '../icons/shield-minus.js';
// ShieldOff aliases
export { default as LucideShieldOff } from '../icons/shield-off.js';
// ShieldPlus aliases
export { default as LucideShieldPlus } from '../icons/shield-plus.js';
// ShieldQuestion aliases
export { default as LucideShieldQuestion } from '../icons/shield-question.js';
// ShieldUser aliases
export { default as LucideShieldUser } from '../icons/shield-user.js';
// Shield aliases
export { default as LucideShield } from '../icons/shield.js';
// ShipWheel aliases
export { default as LucideShipWheel } from '../icons/ship-wheel.js';
// Ship aliases
export { default as LucideShip } from '../icons/ship.js';
// Shirt aliases
export { default as LucideShirt } from '../icons/shirt.js';
// ShoppingBag aliases
export { default as LucideShoppingBag } from '../icons/shopping-bag.js';
// ShoppingCart aliases
export { default as LucideShoppingCart } from '../icons/shopping-cart.js';
// ShoppingBasket aliases
export { default as LucideShoppingBasket } from '../icons/shopping-basket.js';
// Shovel aliases
export { default as LucideShovel } from '../icons/shovel.js';
// ShowerHead aliases
export { default as LucideShowerHead } from '../icons/shower-head.js';
// Shrimp aliases
export { default as LucideShrimp } from '../icons/shrimp.js';
// Shrub aliases
export { default as LucideShrub } from '../icons/shrub.js';
// Shrink aliases
export { default as LucideShrink } from '../icons/shrink.js';
// Shuffle aliases
export { default as LucideShuffle } from '../icons/shuffle.js';
// Sigma aliases
export { default as LucideSigma } from '../icons/sigma.js';
// SignalHigh aliases
export { default as LucideSignalHigh } from '../icons/signal-high.js';
// SignalLow aliases
export { default as LucideSignalLow } from '../icons/signal-low.js';
// SignalMedium aliases
export { default as LucideSignalMedium } from '../icons/signal-medium.js';
// SignalZero aliases
export { default as LucideSignalZero } from '../icons/signal-zero.js';
// Signal aliases
export { default as LucideSignal } from '../icons/signal.js';
// Signature aliases
export { default as LucideSignature } from '../icons/signature.js';
// Signpost aliases
export { default as LucideSignpost } from '../icons/signpost.js';
// SignpostBig aliases
export { default as LucideSignpostBig } from '../icons/signpost-big.js';
// Siren aliases
export { default as LucideSiren } from '../icons/siren.js';
// SkipBack aliases
export { default as LucideSkipBack } from '../icons/skip-back.js';
// SkipForward aliases
export { default as LucideSkipForward } from '../icons/skip-forward.js';
// Skull aliases
export { default as LucideSkull } from '../icons/skull.js';
// Slack aliases
export { default as LucideSlack } from '../icons/slack.js';
// Slash aliases
export { default as LucideSlash } from '../icons/slash.js';
// Slice aliases
export { default as LucideSlice } from '../icons/slice.js';
// SlidersHorizontal aliases
export { default as LucideSlidersHorizontal } from '../icons/sliders-horizontal.js';
// SmartphoneCharging aliases
export { default as LucideSmartphoneCharging } from '../icons/smartphone-charging.js';
// SmartphoneNfc aliases
export { default as LucideSmartphoneNfc } from '../icons/smartphone-nfc.js';
// Smartphone aliases
export { default as LucideSmartphone } from '../icons/smartphone.js';
// SmilePlus aliases
export { default as LucideSmilePlus } from '../icons/smile-plus.js';
// Smile aliases
export { default as LucideSmile } from '../icons/smile.js';
// Snail aliases
export { default as LucideSnail } from '../icons/snail.js';
// Snowflake aliases
export { default as LucideSnowflake } from '../icons/snowflake.js';
// Sofa aliases
export { default as LucideSofa } from '../icons/sofa.js';
// Soup aliases
export { default as LucideSoup } from '../icons/soup.js';
// Space aliases
export { default as LucideSpace } from '../icons/space.js';
// Spade aliases
export { default as LucideSpade } from '../icons/spade.js';
// Sparkle aliases
export { default as LucideSparkle } from '../icons/sparkle.js';
// Speaker aliases
export { default as LucideSpeaker } from '../icons/speaker.js';
// Speech aliases
export { default as LucideSpeech } from '../icons/speech.js';
// SpellCheck2 aliases
export { default as LucideSpellCheck2 } from '../icons/spell-check-2.js';
// SpellCheck aliases
export { default as LucideSpellCheck } from '../icons/spell-check.js';
// Spline aliases
export { default as LucideSpline } from '../icons/spline.js';
// Split aliases
export { default as LucideSplit } from '../icons/split.js';
// SprayCan aliases
export { default as LucideSprayCan } from '../icons/spray-can.js';
// Sprout aliases
export { default as LucideSprout } from '../icons/sprout.js';
// SquareDashedBottom aliases
export { default as LucideSquareDashedBottom } from '../icons/square-dashed-bottom.js';
// SquareDashedBottomCode aliases
export { default as LucideSquareDashedBottomCode } from '../icons/square-dashed-bottom-code.js';
// SquareRadical aliases
export { default as LucideSquareRadical } from '../icons/square-radical.js';
// SquareRoundCorner aliases
export { default as LucideSquareRoundCorner } from '../icons/square-round-corner.js';
// SquareSquare aliases
export { default as LucideSquareSquare } from '../icons/square-square.js';
// SquareStack aliases
export { default as LucideSquareStack } from '../icons/square-stack.js';
// Square aliases
export { default as LucideSquare } from '../icons/square.js';
// Squircle aliases
export { default as LucideSquircle } from '../icons/squircle.js';
// Squirrel aliases
export { default as LucideSquirrel } from '../icons/squirrel.js';
// Stamp aliases
export { default as LucideStamp } from '../icons/stamp.js';
// StarHalf aliases
export { default as LucideStarHalf } from '../icons/star-half.js';
// StarOff aliases
export { default as LucideStarOff } from '../icons/star-off.js';
// Star aliases
export { default as LucideStar } from '../icons/star.js';
// StepBack aliases
export { default as LucideStepBack } from '../icons/step-back.js';
// StepForward aliases
export { default as LucideStepForward } from '../icons/step-forward.js';
// Stethoscope aliases
export { default as LucideStethoscope } from '../icons/stethoscope.js';
// Store aliases
export { default as LucideStore } from '../icons/store.js';
// Sticker aliases
export { default as LucideSticker } from '../icons/sticker.js';
// StickyNote aliases
export { default as LucideStickyNote } from '../icons/sticky-note.js';
// StretchHorizontal aliases
export { default as LucideStretchHorizontal } from '../icons/stretch-horizontal.js';
// StretchVertical aliases
export { default as LucideStretchVertical } from '../icons/stretch-vertical.js';
// SunMedium aliases
export { default as LucideSunMedium } from '../icons/sun-medium.js';
// Subscript aliases
export { default as LucideSubscript } from '../icons/subscript.js';
// Strikethrough aliases
export { default as LucideStrikethrough } from '../icons/strikethrough.js';
// SunDim aliases
export { default as LucideSunDim } from '../icons/sun-dim.js';
// SunMoon aliases
export { default as LucideSunMoon } from '../icons/sun-moon.js';
// SunSnow aliases
export { default as LucideSunSnow } from '../icons/sun-snow.js';
// Sun aliases
export { default as LucideSun } from '../icons/sun.js';
// Sunrise aliases
export { default as LucideSunrise } from '../icons/sunrise.js';
// SwatchBook aliases
export { default as LucideSwatchBook } from '../icons/swatch-book.js';
// Superscript aliases
export { default as LucideSuperscript } from '../icons/superscript.js';
// Sunset aliases
export { default as LucideSunset } from '../icons/sunset.js';
// SwitchCamera aliases
export { default as LucideSwitchCamera } from '../icons/switch-camera.js';
// SwissFranc aliases
export { default as LucideSwissFranc } from '../icons/swiss-franc.js';
// Sword aliases
export { default as LucideSword } from '../icons/sword.js';
// Swords aliases
export { default as LucideSwords } from '../icons/swords.js';
// Syringe aliases
export { default as LucideSyringe } from '../icons/syringe.js';
// Table2 aliases
export { default as LucideTable2 } from '../icons/table-2.js';
// TableCellsSplit aliases
export { default as LucideTableCellsSplit } from '../icons/table-cells-split.js';
// TableCellsMerge aliases
export { default as LucideTableCellsMerge } from '../icons/table-cells-merge.js';
// TableColumnsSplit aliases
export { default as LucideTableColumnsSplit } from '../icons/table-columns-split.js';
// TableOfContents aliases
export { default as LucideTableOfContents } from '../icons/table-of-contents.js';
// TableRowsSplit aliases
export { default as LucideTableRowsSplit } from '../icons/table-rows-split.js';
// TableProperties aliases
export { default as LucideTableProperties } from '../icons/table-properties.js';
// Table aliases
export { default as LucideTable } from '../icons/table.js';
// TabletSmartphone aliases
export { default as LucideTabletSmartphone } from '../icons/tablet-smartphone.js';
// Tablets aliases
export { default as LucideTablets } from '../icons/tablets.js';
// Tag aliases
export { default as LucideTag } from '../icons/tag.js';
// Tablet aliases
export { default as LucideTablet } from '../icons/tablet.js';
// Tags aliases
export { default as LucideTags } from '../icons/tags.js';
// Tally1 aliases
export { default as LucideTally1 } from '../icons/tally-1.js';
// Tally2 aliases
export { default as LucideTally2 } from '../icons/tally-2.js';
// Tally3 aliases
export { default as LucideTally3 } from '../icons/tally-3.js';
// Tally4 aliases
export { default as LucideTally4 } from '../icons/tally-4.js';
// Tally5 aliases
export { default as LucideTally5 } from '../icons/tally-5.js';
// Tangent aliases
export { default as LucideTangent } from '../icons/tangent.js';
// Target aliases
export { default as LucideTarget } from '../icons/target.js';
// Telescope aliases
export { default as LucideTelescope } from '../icons/telescope.js';
// TentTree aliases
export { default as LucideTentTree } from '../icons/tent-tree.js';
// Tent aliases
export { default as LucideTent } from '../icons/tent.js';
// Terminal aliases
export { default as LucideTerminal } from '../icons/terminal.js';
// TestTube aliases
export { default as LucideTestTube } from '../icons/test-tube.js';
// TestTubes aliases
export { default as LucideTestTubes } from '../icons/test-tubes.js';
// TextCursor aliases
export { default as LucideTextCursor } from '../icons/text-cursor.js';
// TextCursorInput aliases
export { default as LucideTextCursorInput } from '../icons/text-cursor-input.js';
// TextQuote aliases
export { default as LucideTextQuote } from '../icons/text-quote.js';
// TextSearch aliases
export { default as LucideTextSearch } from '../icons/text-search.js';
// Text aliases
export { default as LucideText } from '../icons/text.js';
// Theater aliases
export { default as LucideTheater } from '../icons/theater.js';
// ThermometerSnowflake aliases
export { default as LucideThermometerSnowflake } from '../icons/thermometer-snowflake.js';
// ThermometerSun aliases
export { default as LucideThermometerSun } from '../icons/thermometer-sun.js';
// Thermometer aliases
export { default as LucideThermometer } from '../icons/thermometer.js';
// ThumbsDown aliases
export { default as LucideThumbsDown } from '../icons/thumbs-down.js';
// ThumbsUp aliases
export { default as LucideThumbsUp } from '../icons/thumbs-up.js';
// TicketCheck aliases
export { default as LucideTicketCheck } from '../icons/ticket-check.js';
// TicketMinus aliases
export { default as LucideTicketMinus } from '../icons/ticket-minus.js';
// TicketPercent aliases
export { default as LucideTicketPercent } from '../icons/ticket-percent.js';
// TicketPlus aliases
export { default as LucideTicketPlus } from '../icons/ticket-plus.js';
// TicketSlash aliases
export { default as LucideTicketSlash } from '../icons/ticket-slash.js';
// Ticket aliases
export { default as LucideTicket } from '../icons/ticket.js';
// TicketX aliases
export { default as LucideTicketX } from '../icons/ticket-x.js';
// TicketsPlane aliases
export { default as LucideTicketsPlane } from '../icons/tickets-plane.js';
// Tickets aliases
export { default as LucideTickets } from '../icons/tickets.js';
// TimerOff aliases
export { default as LucideTimerOff } from '../icons/timer-off.js';
// TimerReset aliases
export { default as LucideTimerReset } from '../icons/timer-reset.js';
// Timer aliases
export { default as LucideTimer } from '../icons/timer.js';
// ToggleLeft aliases
export { default as LucideToggleLeft } from '../icons/toggle-left.js';
// ToggleRight aliases
export { default as LucideToggleRight } from '../icons/toggle-right.js';
// Toilet aliases
export { default as LucideToilet } from '../icons/toilet.js';
// Torus aliases
export { default as LucideTorus } from '../icons/torus.js';
// Tornado aliases
export { default as LucideTornado } from '../icons/tornado.js';
// TouchpadOff aliases
export { default as LucideTouchpadOff } from '../icons/touchpad-off.js';
// Touchpad aliases
export { default as LucideTouchpad } from '../icons/touchpad.js';
// ToyBrick aliases
export { default as LucideToyBrick } from '../icons/toy-brick.js';
// Tractor aliases
export { default as LucideTractor } from '../icons/tractor.js';
// TowerControl aliases
export { default as LucideTowerControl } from '../icons/tower-control.js';
// TrafficCone aliases
export { default as LucideTrafficCone } from '../icons/traffic-cone.js';
// TrainFrontTunnel aliases
export { default as LucideTrainFrontTunnel } from '../icons/train-front-tunnel.js';
// TrainFront aliases
export { default as LucideTrainFront } from '../icons/train-front.js';
// TrainTrack aliases
export { default as LucideTrainTrack } from '../icons/train-track.js';
// Transgender aliases
export { default as LucideTransgender } from '../icons/transgender.js';
// Trash2 aliases
export { default as LucideTrash2 } from '../icons/trash-2.js';
// Trash aliases
export { default as LucideTrash } from '../icons/trash.js';
// TreeDeciduous aliases
export { default as LucideTreeDeciduous } from '../icons/tree-deciduous.js';
// TreePine aliases
export { default as LucideTreePine } from '../icons/tree-pine.js';
// Trello aliases
export { default as LucideTrello } from '../icons/trello.js';
// Trees aliases
export { default as LucideTrees } from '../icons/trees.js';
// TrendingDown aliases
export { default as LucideTrendingDown } from '../icons/trending-down.js';
// TrendingUpDown aliases
export { default as LucideTrendingUpDown } from '../icons/trending-up-down.js';
// TrendingUp aliases
export { default as LucideTrendingUp } from '../icons/trending-up.js';
// TriangleDashed aliases
export { default as LucideTriangleDashed } from '../icons/triangle-dashed.js';
// TriangleRight aliases
export { default as LucideTriangleRight } from '../icons/triangle-right.js';
// Triangle aliases
export { default as LucideTriangle } from '../icons/triangle.js';
// Trophy aliases
export { default as LucideTrophy } from '../icons/trophy.js';
// Truck aliases
export { default as LucideTruck } from '../icons/truck.js';
// Turtle aliases
export { default as LucideTurtle } from '../icons/turtle.js';
// TvMinimalPlay aliases
export { default as LucideTvMinimalPlay } from '../icons/tv-minimal-play.js';
// Tv aliases
export { default as LucideTv } from '../icons/tv.js';
// Twitch aliases
export { default as LucideTwitch } from '../icons/twitch.js';
// Twitter aliases
export { default as LucideTwitter } from '../icons/twitter.js';
// TypeOutline aliases
export { default as LucideTypeOutline } from '../icons/type-outline.js';
// Type aliases
export { default as LucideType } from '../icons/type.js';
// UmbrellaOff aliases
export { default as LucideUmbrellaOff } from '../icons/umbrella-off.js';
// Umbrella aliases
export { default as LucideUmbrella } from '../icons/umbrella.js';
// Underline aliases
export { default as LucideUnderline } from '../icons/underline.js';
// Undo2 aliases
export { default as LucideUndo2 } from '../icons/undo-2.js';
// UndoDot aliases
export { default as LucideUndoDot } from '../icons/undo-dot.js';
// Undo aliases
export { default as LucideUndo } from '../icons/undo.js';
// UnfoldHorizontal aliases
export { default as LucideUnfoldHorizontal } from '../icons/unfold-horizontal.js';
// UnfoldVertical aliases
export { default as LucideUnfoldVertical } from '../icons/unfold-vertical.js';
// Ungroup aliases
export { default as LucideUngroup } from '../icons/ungroup.js';
// Unlink2 aliases
export { default as LucideUnlink2 } from '../icons/unlink-2.js';
// Unlink aliases
export { default as LucideUnlink } from '../icons/unlink.js';
// Unplug aliases
export { default as LucideUnplug } from '../icons/unplug.js';
// Upload aliases
export { default as LucideUpload } from '../icons/upload.js';
// Usb aliases
export { default as LucideUsb } from '../icons/usb.js';
// UserCheck aliases
export { default as LucideUserCheck } from '../icons/user-check.js';
// UserCog aliases
export { default as LucideUserCog } from '../icons/user-cog.js';
// UserMinus aliases
export { default as LucideUserMinus } from '../icons/user-minus.js';
// UserPen aliases
export { default as LucideUserPen } from '../icons/user-pen.js';
// UserPlus aliases
export { default as LucideUserPlus } from '../icons/user-plus.js';
// UserRoundSearch aliases
export { default as LucideUserRoundSearch } from '../icons/user-round-search.js';
// UserRoundPen aliases
export { default as LucideUserRoundPen } from '../icons/user-round-pen.js';
// UserSearch aliases
export { default as LucideUserSearch } from '../icons/user-search.js';
// UserX aliases
export { default as LucideUserX } from '../icons/user-x.js';
// User aliases
export { default as LucideUser } from '../icons/user.js';
// Users aliases
export { default as LucideUsers } from '../icons/users.js';
// UtilityPole aliases
export { default as LucideUtilityPole } from '../icons/utility-pole.js';
// Variable aliases
export { default as LucideVariable } from '../icons/variable.js';
// Vault aliases
export { default as LucideVault } from '../icons/vault.js';
// Vegan aliases
export { default as LucideVegan } from '../icons/vegan.js';
// VenetianMask aliases
export { default as LucideVenetianMask } from '../icons/venetian-mask.js';
// VenusAndMars aliases
export { default as LucideVenusAndMars } from '../icons/venus-and-mars.js';
// Venus aliases
export { default as LucideVenus } from '../icons/venus.js';
// VibrateOff aliases
export { default as LucideVibrateOff } from '../icons/vibrate-off.js';
// Vibrate aliases
export { default as LucideVibrate } from '../icons/vibrate.js';
// VideoOff aliases
export { default as LucideVideoOff } from '../icons/video-off.js';
// Video aliases
export { default as LucideVideo } from '../icons/video.js';
// Videotape aliases
export { default as LucideVideotape } from '../icons/videotape.js';
// View aliases
export { default as LucideView } from '../icons/view.js';
// Voicemail aliases
export { default as LucideVoicemail } from '../icons/voicemail.js';
// Volume1 aliases
export { default as LucideVolume1 } from '../icons/volume-1.js';
// Volleyball aliases
export { default as LucideVolleyball } from '../icons/volleyball.js';
// Volume2 aliases
export { default as LucideVolume2 } from '../icons/volume-2.js';
// VolumeOff aliases
export { default as LucideVolumeOff } from '../icons/volume-off.js';
// Volume aliases
export { default as LucideVolume } from '../icons/volume.js';
// VolumeX aliases
export { default as LucideVolumeX } from '../icons/volume-x.js';
// Vote aliases
export { default as LucideVote } from '../icons/vote.js';
// WalletCards aliases
export { default as LucideWalletCards } from '../icons/wallet-cards.js';
// Wallet aliases
export { default as LucideWallet } from '../icons/wallet.js';
// Wallpaper aliases
export { default as LucideWallpaper } from '../icons/wallpaper.js';
// Wand aliases
export { default as LucideWand } from '../icons/wand.js';
// Warehouse aliases
export { default as LucideWarehouse } from '../icons/warehouse.js';
// WashingMachine aliases
export { default as LucideWashingMachine } from '../icons/washing-machine.js';
// Watch aliases
export { default as LucideWatch } from '../icons/watch.js';
// WavesLadder aliases
export { default as LucideWavesLadder } from '../icons/waves-ladder.js';
// Waves aliases
export { default as LucideWaves } from '../icons/waves.js';
// Waypoints aliases
export { default as LucideWaypoints } from '../icons/waypoints.js';
// Webcam aliases
export { default as LucideWebcam } from '../icons/webcam.js';
// WebhookOff aliases
export { default as LucideWebhookOff } from '../icons/webhook-off.js';
// Weight aliases
export { default as LucideWeight } from '../icons/weight.js';
// Webhook aliases
export { default as LucideWebhook } from '../icons/webhook.js';
// WheatOff aliases
export { default as LucideWheatOff } from '../icons/wheat-off.js';
// Wheat aliases
export { default as LucideWheat } from '../icons/wheat.js';
// WholeWord aliases
export { default as LucideWholeWord } from '../icons/whole-word.js';
// WifiHigh aliases
export { default as LucideWifiHigh } from '../icons/wifi-high.js';
// WifiOff aliases
export { default as LucideWifiOff } from '../icons/wifi-off.js';
// WifiLow aliases
export { default as LucideWifiLow } from '../icons/wifi-low.js';
// WifiZero aliases
export { default as LucideWifiZero } from '../icons/wifi-zero.js';
// Wifi aliases
export { default as LucideWifi } from '../icons/wifi.js';
// WindArrowDown aliases
export { default as LucideWindArrowDown } from '../icons/wind-arrow-down.js';
// Wind aliases
export { default as LucideWind } from '../icons/wind.js';
// WineOff aliases
export { default as LucideWineOff } from '../icons/wine-off.js';
// Wine aliases
export { default as LucideWine } from '../icons/wine.js';
// Workflow aliases
export { default as LucideWorkflow } from '../icons/workflow.js';
// Worm aliases
export { default as LucideWorm } from '../icons/worm.js';
// Wrench aliases
export { default as LucideWrench } from '../icons/wrench.js';
// WrapText aliases
export { default as LucideWrapText } from '../icons/wrap-text.js';
// X aliases
export { default as LucideX } from '../icons/x.js';
// Youtube aliases
export { default as LucideYoutube } from '../icons/youtube.js';
// ZapOff aliases
export { default as LucideZapOff } from '../icons/zap-off.js';
// Zap aliases
export { default as LucideZap } from '../icons/zap.js';
// ZoomIn aliases
export { default as LucideZoomIn } from '../icons/zoom-in.js';
// ZoomOut aliases
export { default as LucideZoomOut } from '../icons/zoom-out.js';
// ArrowDown01 aliases
export { default as LucideArrowDown01 } from '../icons/arrow-down-0-1.js';
// ArrowDown10 aliases
export { default as LucideArrowDown10 } from '../icons/arrow-down-1-0.js';
// ArrowUp01 aliases
export { default as LucideArrowUp01 } from '../icons/arrow-up-0-1.js';
// ArrowUp10 aliases
export { default as LucideArrowUp10 } from '../icons/arrow-up-1-0.js';
// AlarmClockCheck aliases
export { default as LucideAlarmClockCheck } from '../icons/alarm-clock-check.js';
export { default as LucideAlarmCheck } from '../icons/alarm-check.js';
// AlarmClockMinus aliases
export { default as LucideAlarmClockMinus } from '../icons/alarm-clock-minus.js';
export { default as LucideAlarmMinus } from '../icons/alarm-minus.js';
// AlarmClockPlus aliases
export { default as LucideAlarmClockPlus } from '../icons/alarm-clock-plus.js';
export { default as LucideAlarmPlus } from '../icons/alarm-plus.js';
// ArrowDownAZ aliases
export { default as LucideArrowDownAZ } from '../icons/arrow-down-a-z.js';
export { default as LucideArrowDownAz } from '../icons/arrow-down-az.js';
// ArrowDownZA aliases
export { default as LucideArrowDownZA } from '../icons/arrow-down-z-a.js';
export { default as LucideArrowDownZa } from '../icons/arrow-down-za.js';
// ArrowDownWideNarrow aliases
export { default as LucideArrowDownWideNarrow } from '../icons/arrow-down-wide-narrow.js';
export { default as LucideSortDesc } from '../icons/sort-desc.js';
// ArrowUpAZ aliases
export { default as LucideArrowUpAZ } from '../icons/arrow-up-a-z.js';
export { default as LucideArrowUpAz } from '../icons/arrow-up-az.js';
// ArrowUpNarrowWide aliases
export { default as LucideArrowUpNarrowWide } from '../icons/arrow-up-narrow-wide.js';
export { default as LucideSortAsc } from '../icons/sort-asc.js';
// ArrowUpZA aliases
export { default as LucideArrowUpZA } from '../icons/arrow-up-z-a.js';
export { default as LucideArrowUpZa } from '../icons/arrow-up-za.js';
// Axis3d aliases
export { default as LucideAxis3d } from '../icons/axis-3d.js';
export { default as LucideAxis3D } from '../icons/axis-3-d.js';
// BadgeCheck aliases
export { default as LucideBadgeCheck } from '../icons/badge-check.js';
export { default as LucideVerified } from '../icons/verified.js';
// BetweenHorizontalEnd aliases
export { default as LucideBetweenHorizontalEnd } from '../icons/between-horizontal-end.js';
export { 
/** @deprecated Renamed because of typo, use {@link BetweenHorizontalEnd} instead. This alias will be removed in v1.0 */
default as LucideBetweenHorizonalEnd } from '../icons/between-horizonal-end.js';
// BetweenHorizontalStart aliases
export { default as LucideBetweenHorizontalStart } from '../icons/between-horizontal-start.js';
export { 
/** @deprecated Renamed because of typo, use {@link BetweenHorizontalStart} instead. This alias will be removed in v1.0 */
default as LucideBetweenHorizonalStart } from '../icons/between-horizonal-start.js';
// BookDashed aliases
export { default as LucideBookDashed } from '../icons/book-dashed.js';
export { default as LucideBookTemplate } from '../icons/book-template.js';
// Braces aliases
export { default as LucideBraces } from '../icons/braces.js';
export { default as LucideCurlyBraces } from '../icons/curly-braces.js';
// Captions aliases
export { default as LucideCaptions } from '../icons/captions.js';
export { default as LucideSubtitles } from '../icons/subtitles.js';
// ChartArea aliases
export { default as LucideChartArea } from '../icons/chart-area.js';
export { 
/** @deprecated  */
default as LucideAreaChart } from '../icons/area-chart.js';
// ChartBarBig aliases
export { default as LucideChartBarBig } from '../icons/chart-bar-big.js';
export { 
/** @deprecated  */
default as LucideBarChartHorizontalBig } from '../icons/bar-chart-horizontal-big.js';
// ChartBar aliases
export { default as LucideChartBar } from '../icons/chart-bar.js';
export { 
/** @deprecated  */
default as LucideBarChartHorizontal } from '../icons/bar-chart-horizontal.js';
// ChartCandlestick aliases
export { default as LucideChartCandlestick } from '../icons/chart-candlestick.js';
export { 
/** @deprecated  */
default as LucideCandlestickChart } from '../icons/candlestick-chart.js';
// ChartColumnBig aliases
export { default as LucideChartColumnBig } from '../icons/chart-column-big.js';
export { 
/** @deprecated  */
default as LucideBarChartBig } from '../icons/bar-chart-big.js';
// ChartColumnIncreasing aliases
export { default as LucideChartColumnIncreasing } from '../icons/chart-column-increasing.js';
export { 
/** @deprecated  */
default as LucideBarChart4 } from '../icons/bar-chart-4.js';
// ChartColumn aliases
export { default as LucideChartColumn } from '../icons/chart-column.js';
export { 
/** @deprecated  */
default as LucideBarChart3 } from '../icons/bar-chart-3.js';
// ChartLine aliases
export { default as LucideChartLine } from '../icons/chart-line.js';
export { 
/** @deprecated  */
default as LucideLineChart } from '../icons/line-chart.js';
// ChartNoAxesColumnIncreasing aliases
export { default as LucideChartNoAxesColumnIncreasing } from '../icons/chart-no-axes-column-increasing.js';
export { 
/** @deprecated  */
default as LucideBarChart } from '../icons/bar-chart.js';
// ChartNoAxesColumn aliases
export { default as LucideChartNoAxesColumn } from '../icons/chart-no-axes-column.js';
export { 
/** @deprecated  */
default as LucideBarChart2 } from '../icons/bar-chart-2.js';
// ChartNoAxesGantt aliases
export { default as LucideChartNoAxesGantt } from '../icons/chart-no-axes-gantt.js';
export { 
/** @deprecated  */
default as LucideGanttChart } from '../icons/gantt-chart.js';
// ChartPie aliases
export { default as LucideChartPie } from '../icons/chart-pie.js';
export { 
/** @deprecated  */
default as LucidePieChart } from '../icons/pie-chart.js';
// ChartScatter aliases
export { default as LucideChartScatter } from '../icons/chart-scatter.js';
export { 
/** @deprecated  */
default as LucideScatterChart } from '../icons/scatter-chart.js';
// CircleAlert aliases
export { default as LucideCircleAlert } from '../icons/circle-alert.js';
export { default as LucideAlertCircle } from '../icons/alert-circle.js';
// CircleArrowLeft aliases
export { default as LucideCircleArrowLeft } from '../icons/circle-arrow-left.js';
export { default as LucideArrowLeftCircle } from '../icons/arrow-left-circle.js';
// CircleArrowDown aliases
export { default as LucideCircleArrowDown } from '../icons/circle-arrow-down.js';
export { default as LucideArrowDownCircle } from '../icons/arrow-down-circle.js';
// CircleArrowOutDownLeft aliases
export { default as LucideCircleArrowOutDownLeft } from '../icons/circle-arrow-out-down-left.js';
export { default as LucideArrowDownLeftFromCircle } from '../icons/arrow-down-left-from-circle.js';
// CircleArrowOutDownRight aliases
export { default as LucideCircleArrowOutDownRight } from '../icons/circle-arrow-out-down-right.js';
export { default as LucideArrowDownRightFromCircle } from '../icons/arrow-down-right-from-circle.js';
// CircleArrowOutUpLeft aliases
export { default as LucideCircleArrowOutUpLeft } from '../icons/circle-arrow-out-up-left.js';
export { default as LucideArrowUpLeftFromCircle } from '../icons/arrow-up-left-from-circle.js';
// CircleArrowUp aliases
export { default as LucideCircleArrowUp } from '../icons/circle-arrow-up.js';
export { default as LucideArrowUpCircle } from '../icons/arrow-up-circle.js';
// CircleArrowOutUpRight aliases
export { default as LucideCircleArrowOutUpRight } from '../icons/circle-arrow-out-up-right.js';
export { default as LucideArrowUpRightFromCircle } from '../icons/arrow-up-right-from-circle.js';
// CircleArrowRight aliases
export { default as LucideCircleArrowRight } from '../icons/circle-arrow-right.js';
export { default as LucideArrowRightCircle } from '../icons/arrow-right-circle.js';
// CircleCheckBig aliases
export { default as LucideCircleCheckBig } from '../icons/circle-check-big.js';
export { default as LucideCheckCircle } from '../icons/check-circle.js';
// CircleChevronDown aliases
export { default as LucideCircleChevronDown } from '../icons/circle-chevron-down.js';
export { default as LucideChevronDownCircle } from '../icons/chevron-down-circle.js';
// CircleCheck aliases
export { default as LucideCircleCheck } from '../icons/circle-check.js';
export { default as LucideCheckCircle2 } from '../icons/check-circle-2.js';
// CircleChevronLeft aliases
export { default as LucideCircleChevronLeft } from '../icons/circle-chevron-left.js';
export { default as LucideChevronLeftCircle } from '../icons/chevron-left-circle.js';
// CircleChevronRight aliases
export { default as LucideCircleChevronRight } from '../icons/circle-chevron-right.js';
export { default as LucideChevronRightCircle } from '../icons/chevron-right-circle.js';
// CircleChevronUp aliases
export { default as LucideCircleChevronUp } from '../icons/circle-chevron-up.js';
export { default as LucideChevronUpCircle } from '../icons/chevron-up-circle.js';
// CircleDivide aliases
export { default as LucideCircleDivide } from '../icons/circle-divide.js';
export { default as LucideDivideCircle } from '../icons/divide-circle.js';
// CircleGauge aliases
export { default as LucideCircleGauge } from '../icons/circle-gauge.js';
export { default as LucideGaugeCircle } from '../icons/gauge-circle.js';
// CircleHelp aliases
export { default as LucideCircleHelp } from '../icons/circle-help.js';
export { default as LucideHelpCircle } from '../icons/help-circle.js';
// CircleMinus aliases
export { default as LucideCircleMinus } from '../icons/circle-minus.js';
export { default as LucideMinusCircle } from '../icons/minus-circle.js';
// CircleParking aliases
export { default as LucideCircleParking } from '../icons/circle-parking.js';
export { default as LucideParkingCircle } from '../icons/parking-circle.js';
// CircleParkingOff aliases
export { default as LucideCircleParkingOff } from '../icons/circle-parking-off.js';
export { default as LucideParkingCircleOff } from '../icons/parking-circle-off.js';
// CirclePause aliases
export { default as LucideCirclePause } from '../icons/circle-pause.js';
export { default as LucidePauseCircle } from '../icons/pause-circle.js';
// CirclePlay aliases
export { default as LucideCirclePlay } from '../icons/circle-play.js';
export { default as LucidePlayCircle } from '../icons/play-circle.js';
// CirclePercent aliases
export { default as LucideCirclePercent } from '../icons/circle-percent.js';
export { default as LucidePercentCircle } from '../icons/percent-circle.js';
// CirclePlus aliases
export { default as LucideCirclePlus } from '../icons/circle-plus.js';
export { default as LucidePlusCircle } from '../icons/plus-circle.js';
// CirclePower aliases
export { default as LucideCirclePower } from '../icons/circle-power.js';
export { default as LucidePowerCircle } from '../icons/power-circle.js';
// CircleSlash2 aliases
export { default as LucideCircleSlash2 } from '../icons/circle-slash-2.js';
export { default as LucideCircleSlashed } from '../icons/circle-slashed.js';
// CircleStop aliases
export { default as LucideCircleStop } from '../icons/circle-stop.js';
export { default as LucideStopCircle } from '../icons/stop-circle.js';
// CircleUserRound aliases
export { default as LucideCircleUserRound } from '../icons/circle-user-round.js';
export { 
/** @deprecated  */
default as LucideUserCircle2 } from '../icons/user-circle-2.js';
// CircleUser aliases
export { default as LucideCircleUser } from '../icons/circle-user.js';
export { default as LucideUserCircle } from '../icons/user-circle.js';
// CircleX aliases
export { default as LucideCircleX } from '../icons/circle-x.js';
export { default as LucideXCircle } from '../icons/x-circle.js';
// ClipboardPenLine aliases
export { default as LucideClipboardPenLine } from '../icons/clipboard-pen-line.js';
export { default as LucideClipboardSignature } from '../icons/clipboard-signature.js';
// ClipboardPen aliases
export { default as LucideClipboardPen } from '../icons/clipboard-pen.js';
export { default as LucideClipboardEdit } from '../icons/clipboard-edit.js';
// CloudDownload aliases
export { default as LucideCloudDownload } from '../icons/cloud-download.js';
export { default as LucideDownloadCloud } from '../icons/download-cloud.js';
// CloudUpload aliases
export { default as LucideCloudUpload } from '../icons/cloud-upload.js';
export { default as LucideUploadCloud } from '../icons/upload-cloud.js';
// CodeXml aliases
export { default as LucideCodeXml } from '../icons/code-xml.js';
export { 
/** @deprecated  */
default as LucideCode2 } from '../icons/code-2.js';
// Columns2 aliases
export { default as LucideColumns2 } from '../icons/columns-2.js';
export { default as LucideColumns } from '../icons/columns.js';
// Columns3 aliases
export { default as LucideColumns3 } from '../icons/columns-3.js';
export { default as LucidePanelsLeftRight } from '../icons/panels-left-right.js';
// ContactRound aliases
export { default as LucideContactRound } from '../icons/contact-round.js';
export { 
/** @deprecated  */
default as LucideContact2 } from '../icons/contact-2.js';
// DiamondPercent aliases
export { default as LucideDiamondPercent } from '../icons/diamond-percent.js';
export { default as LucidePercentDiamond } from '../icons/percent-diamond.js';
// Earth aliases
export { default as LucideEarth } from '../icons/earth.js';
export { 
/** @deprecated  */
default as LucideGlobe2 } from '../icons/globe-2.js';
// EllipsisVertical aliases
export { default as LucideEllipsisVertical } from '../icons/ellipsis-vertical.js';
export { default as LucideMoreVertical } from '../icons/more-vertical.js';
// Ellipsis aliases
export { default as LucideEllipsis } from '../icons/ellipsis.js';
export { default as LucideMoreHorizontal } from '../icons/more-horizontal.js';
// FileAxis3d aliases
export { default as LucideFileAxis3d } from '../icons/file-axis-3d.js';
export { default as LucideFileAxis3D } from '../icons/file-axis-3-d.js';
// FileChartColumnIncreasing aliases
export { default as LucideFileChartColumnIncreasing } from '../icons/file-chart-column-increasing.js';
export { 
/** @deprecated  */
default as LucideFileBarChart } from '../icons/file-bar-chart.js';
// FileChartColumn aliases
export { default as LucideFileChartColumn } from '../icons/file-chart-column.js';
export { 
/** @deprecated  */
default as LucideFileBarChart2 } from '../icons/file-bar-chart-2.js';
// FileChartLine aliases
export { default as LucideFileChartLine } from '../icons/file-chart-line.js';
export { 
/** @deprecated  */
default as LucideFileLineChart } from '../icons/file-line-chart.js';
// FileChartPie aliases
export { default as LucideFileChartPie } from '../icons/file-chart-pie.js';
export { 
/** @deprecated  */
default as LucideFilePieChart } from '../icons/file-pie-chart.js';
// FileCog aliases
export { default as LucideFileCog } from '../icons/file-cog.js';
export { 
/** @deprecated  */
default as LucideFileCog2 } from '../icons/file-cog-2.js';
// FilePenLine aliases
export { default as LucideFilePenLine } from '../icons/file-pen-line.js';
export { default as LucideFileSignature } from '../icons/file-signature.js';
// FilePen aliases
export { default as LucideFilePen } from '../icons/file-pen.js';
export { default as LucideFileEdit } from '../icons/file-edit.js';
// FolderCog aliases
export { default as LucideFolderCog } from '../icons/folder-cog.js';
export { 
/** @deprecated  */
default as LucideFolderCog2 } from '../icons/folder-cog-2.js';
// FolderPen aliases
export { default as LucideFolderPen } from '../icons/folder-pen.js';
export { default as LucideFolderEdit } from '../icons/folder-edit.js';
// GitCommitHorizontal aliases
export { default as LucideGitCommitHorizontal } from '../icons/git-commit-horizontal.js';
export { default as LucideGitCommit } from '../icons/git-commit.js';
// Grid2x2Check aliases
export { default as LucideGrid2x2Check } from '../icons/grid-2x2-check.js';
export { default as LucideGrid2X2Check } from '../icons/grid-2-x-2-check.js';
// Grid2x2Plus aliases
export { default as LucideGrid2x2Plus } from '../icons/grid-2x2-plus.js';
export { default as LucideGrid2X2Plus } from '../icons/grid-2-x-2-plus.js';
// Grid2x2X aliases
export { default as LucideGrid2x2X } from '../icons/grid-2x2-x.js';
export { default as LucideGrid2X2X } from '../icons/grid-2-x-2-x.js';
// Grid2x2 aliases
export { default as LucideGrid2x2 } from '../icons/grid-2x2.js';
export { default as LucideGrid2X2 } from '../icons/grid-2-x-2.js';
// Grid3x3 aliases
export { default as LucideGrid3x3 } from '../icons/grid-3x3.js';
export { default as LucideGrid } from '../icons/grid.js';
export { default as LucideGrid3X3 } from '../icons/grid-3-x-3.js';
// HandHelping aliases
export { default as LucideHandHelping } from '../icons/hand-helping.js';
export { default as LucideHelpingHand } from '../icons/helping-hand.js';
// House aliases
export { default as LucideHouse } from '../icons/house.js';
export { default as LucideHome } from '../icons/home.js';
// IceCreamBowl aliases
export { default as LucideIceCreamBowl } from '../icons/ice-cream-bowl.js';
export { 
/** @deprecated  */
default as LucideIceCream2 } from '../icons/ice-cream-2.js';
// IceCreamCone aliases
export { default as LucideIceCreamCone } from '../icons/ice-cream-cone.js';
export { default as LucideIceCream } from '../icons/ice-cream.js';
// IndentDecrease aliases
export { default as LucideIndentDecrease } from '../icons/indent-decrease.js';
export { default as LucideOutdent } from '../icons/outdent.js';
// IndentIncrease aliases
export { default as LucideIndentIncrease } from '../icons/indent-increase.js';
export { default as LucideIndent } from '../icons/indent.js';
// LaptopMinimal aliases
export { default as LucideLaptopMinimal } from '../icons/laptop-minimal.js';
export { 
/** @deprecated  */
default as LucideLaptop2 } from '../icons/laptop-2.js';
// Layers aliases
export { default as LucideLayers } from '../icons/layers.js';
export { default as LucideLayers3 } from '../icons/layers-3.js';
// LoaderCircle aliases
export { default as LucideLoaderCircle } from '../icons/loader-circle.js';
export { default as LucideLoader2 } from '../icons/loader-2.js';
// LockKeyholeOpen aliases
export { default as LucideLockKeyholeOpen } from '../icons/lock-keyhole-open.js';
export { default as LucideUnlockKeyhole } from '../icons/unlock-keyhole.js';
// LockOpen aliases
export { default as LucideLockOpen } from '../icons/lock-open.js';
export { default as LucideUnlock } from '../icons/unlock.js';
// MicVocal aliases
export { default as LucideMicVocal } from '../icons/mic-vocal.js';
export { 
/** @deprecated  */
default as LucideMic2 } from '../icons/mic-2.js';
// Move3d aliases
export { default as LucideMove3d } from '../icons/move-3d.js';
export { default as LucideMove3D } from '../icons/move-3-d.js';
// OctagonAlert aliases
export { default as LucideOctagonAlert } from '../icons/octagon-alert.js';
export { default as LucideAlertOctagon } from '../icons/alert-octagon.js';
// OctagonPause aliases
export { default as LucideOctagonPause } from '../icons/octagon-pause.js';
export { default as LucidePauseOctagon } from '../icons/pause-octagon.js';
// OctagonX aliases
export { default as LucideOctagonX } from '../icons/octagon-x.js';
export { default as LucideXOctagon } from '../icons/x-octagon.js';
// PaintbrushVertical aliases
export { default as LucidePaintbrushVertical } from '../icons/paintbrush-vertical.js';
export { default as LucidePaintbrush2 } from '../icons/paintbrush-2.js';
// PanelBottomDashed aliases
export { default as LucidePanelBottomDashed } from '../icons/panel-bottom-dashed.js';
export { default as LucidePanelBottomInactive } from '../icons/panel-bottom-inactive.js';
// PanelLeftDashed aliases
export { default as LucidePanelLeftDashed } from '../icons/panel-left-dashed.js';
export { default as LucidePanelLeftInactive } from '../icons/panel-left-inactive.js';
// PanelLeftClose aliases
export { default as LucidePanelLeftClose } from '../icons/panel-left-close.js';
export { default as LucideSidebarClose } from '../icons/sidebar-close.js';
// PanelLeftOpen aliases
export { default as LucidePanelLeftOpen } from '../icons/panel-left-open.js';
export { default as LucideSidebarOpen } from '../icons/sidebar-open.js';
// PanelLeft aliases
export { default as LucidePanelLeft } from '../icons/panel-left.js';
export { default as LucideSidebar } from '../icons/sidebar.js';
// PanelRightDashed aliases
export { default as LucidePanelRightDashed } from '../icons/panel-right-dashed.js';
export { default as LucidePanelRightInactive } from '../icons/panel-right-inactive.js';
// PanelTopDashed aliases
export { default as LucidePanelTopDashed } from '../icons/panel-top-dashed.js';
export { default as LucidePanelTopInactive } from '../icons/panel-top-inactive.js';
// PanelsTopLeft aliases
export { default as LucidePanelsTopLeft } from '../icons/panels-top-left.js';
export { default as LucideLayout } from '../icons/layout.js';
// PenLine aliases
export { default as LucidePenLine } from '../icons/pen-line.js';
export { 
/** @deprecated  */
default as LucideEdit3 } from '../icons/edit-3.js';
// Pen aliases
export { default as LucidePen } from '../icons/pen.js';
export { 
/** @deprecated  */
default as LucideEdit2 } from '../icons/edit-2.js';
// PlugZap aliases
export { default as LucidePlugZap } from '../icons/plug-zap.js';
export { default as LucidePlugZap2 } from '../icons/plug-zap-2.js';
// RectangleEllipsis aliases
export { default as LucideRectangleEllipsis } from '../icons/rectangle-ellipsis.js';
export { default as LucideFormInput } from '../icons/form-input.js';
// Rotate3d aliases
export { default as LucideRotate3d } from '../icons/rotate-3d.js';
export { default as LucideRotate3D } from '../icons/rotate-3-d.js';
// Rows2 aliases
export { default as LucideRows2 } from '../icons/rows-2.js';
export { default as LucideRows } from '../icons/rows.js';
// Rows3 aliases
export { default as LucideRows3 } from '../icons/rows-3.js';
export { default as LucidePanelsTopBottom } from '../icons/panels-top-bottom.js';
// Scale3d aliases
export { default as LucideScale3d } from '../icons/scale-3d.js';
export { default as LucideScale3D } from '../icons/scale-3-d.js';
// SendHorizontal aliases
export { default as LucideSendHorizontal } from '../icons/send-horizontal.js';
export { 
/** @deprecated Renamed because of typo, use {@link SendHorizontal} instead. This alias will be removed in v1.0 */
default as LucideSendHorizonal } from '../icons/send-horizonal.js';
// ShieldX aliases
export { default as LucideShieldX } from '../icons/shield-x.js';
export { default as LucideShieldClose } from '../icons/shield-close.js';
// SlidersVertical aliases
export { default as LucideSlidersVertical } from '../icons/sliders-vertical.js';
export { default as LucideSliders } from '../icons/sliders.js';
// Sparkles aliases
export { default as LucideSparkles } from '../icons/sparkles.js';
export { default as LucideStars } from '../icons/stars.js';
// SquareActivity aliases
export { default as LucideSquareActivity } from '../icons/square-activity.js';
export { default as LucideActivitySquare } from '../icons/activity-square.js';
// SquareArrowDownLeft aliases
export { default as LucideSquareArrowDownLeft } from '../icons/square-arrow-down-left.js';
export { default as LucideArrowDownLeftSquare } from '../icons/arrow-down-left-square.js';
// SquareArrowDownRight aliases
export { default as LucideSquareArrowDownRight } from '../icons/square-arrow-down-right.js';
export { default as LucideArrowDownRightSquare } from '../icons/arrow-down-right-square.js';
// SquareArrowDown aliases
export { default as LucideSquareArrowDown } from '../icons/square-arrow-down.js';
export { default as LucideArrowDownSquare } from '../icons/arrow-down-square.js';
// SquareArrowLeft aliases
export { default as LucideSquareArrowLeft } from '../icons/square-arrow-left.js';
export { default as LucideArrowLeftSquare } from '../icons/arrow-left-square.js';
// SquareArrowOutDownLeft aliases
export { default as LucideSquareArrowOutDownLeft } from '../icons/square-arrow-out-down-left.js';
export { default as LucideArrowDownLeftFromSquare } from '../icons/arrow-down-left-from-square.js';
// SquareArrowOutDownRight aliases
export { default as LucideSquareArrowOutDownRight } from '../icons/square-arrow-out-down-right.js';
export { default as LucideArrowDownRightFromSquare } from '../icons/arrow-down-right-from-square.js';
// SquareArrowOutUpLeft aliases
export { default as LucideSquareArrowOutUpLeft } from '../icons/square-arrow-out-up-left.js';
export { default as LucideArrowUpLeftFromSquare } from '../icons/arrow-up-left-from-square.js';
// SquareArrowRight aliases
export { default as LucideSquareArrowRight } from '../icons/square-arrow-right.js';
export { default as LucideArrowRightSquare } from '../icons/arrow-right-square.js';
// SquareArrowOutUpRight aliases
export { default as LucideSquareArrowOutUpRight } from '../icons/square-arrow-out-up-right.js';
export { default as LucideArrowUpRightFromSquare } from '../icons/arrow-up-right-from-square.js';
// SquareArrowUpLeft aliases
export { default as LucideSquareArrowUpLeft } from '../icons/square-arrow-up-left.js';
export { default as LucideArrowUpLeftSquare } from '../icons/arrow-up-left-square.js';
// SquareArrowUpRight aliases
export { default as LucideSquareArrowUpRight } from '../icons/square-arrow-up-right.js';
export { default as LucideArrowUpRightSquare } from '../icons/arrow-up-right-square.js';
// SquareArrowUp aliases
export { default as LucideSquareArrowUp } from '../icons/square-arrow-up.js';
export { default as LucideArrowUpSquare } from '../icons/arrow-up-square.js';
// SquareAsterisk aliases
export { default as LucideSquareAsterisk } from '../icons/square-asterisk.js';
export { default as LucideAsteriskSquare } from '../icons/asterisk-square.js';
// SquareBottomDashedScissors aliases
export { default as LucideSquareBottomDashedScissors } from '../icons/square-bottom-dashed-scissors.js';
export { default as LucideScissorsSquareDashedBottom } from '../icons/scissors-square-dashed-bottom.js';
// SquareChartGantt aliases
export { default as LucideSquareChartGantt } from '../icons/square-chart-gantt.js';
export { default as LucideGanttChartSquare } from '../icons/gantt-chart-square.js';
export { default as LucideSquareGanttChart } from '../icons/square-gantt-chart.js';
// SquareCheckBig aliases
export { default as LucideSquareCheckBig } from '../icons/square-check-big.js';
export { default as LucideCheckSquare } from '../icons/check-square.js';
// SquareCheck aliases
export { default as LucideSquareCheck } from '../icons/square-check.js';
export { default as LucideCheckSquare2 } from '../icons/check-square-2.js';
// SquareChevronDown aliases
export { default as LucideSquareChevronDown } from '../icons/square-chevron-down.js';
export { default as LucideChevronDownSquare } from '../icons/chevron-down-square.js';
// SquareChevronLeft aliases
export { default as LucideSquareChevronLeft } from '../icons/square-chevron-left.js';
export { default as LucideChevronLeftSquare } from '../icons/chevron-left-square.js';
// SquareChevronRight aliases
export { default as LucideSquareChevronRight } from '../icons/square-chevron-right.js';
export { default as LucideChevronRightSquare } from '../icons/chevron-right-square.js';
// SquareChevronUp aliases
export { default as LucideSquareChevronUp } from '../icons/square-chevron-up.js';
export { default as LucideChevronUpSquare } from '../icons/chevron-up-square.js';
// SquareCode aliases
export { default as LucideSquareCode } from '../icons/square-code.js';
export { default as LucideCodeSquare } from '../icons/code-square.js';
// SquareDashedKanban aliases
export { default as LucideSquareDashedKanban } from '../icons/square-dashed-kanban.js';
export { default as LucideKanbanSquareDashed } from '../icons/kanban-square-dashed.js';
// SquareDashedMousePointer aliases
export { default as LucideSquareDashedMousePointer } from '../icons/square-dashed-mouse-pointer.js';
export { default as LucideMousePointerSquareDashed } from '../icons/mouse-pointer-square-dashed.js';
// SquareDashed aliases
export { default as LucideSquareDashed } from '../icons/square-dashed.js';
export { default as LucideBoxSelect } from '../icons/box-select.js';
// SquareDot aliases
export { default as LucideSquareDot } from '../icons/square-dot.js';
export { default as LucideDotSquare } from '../icons/dot-square.js';
// SquareDivide aliases
export { default as LucideSquareDivide } from '../icons/square-divide.js';
export { default as LucideDivideSquare } from '../icons/divide-square.js';
// SquareEqual aliases
export { default as LucideSquareEqual } from '../icons/square-equal.js';
export { default as LucideEqualSquare } from '../icons/equal-square.js';
// SquareFunction aliases
export { default as LucideSquareFunction } from '../icons/square-function.js';
export { default as LucideFunctionSquare } from '../icons/function-square.js';
// SquareKanban aliases
export { default as LucideSquareKanban } from '../icons/square-kanban.js';
export { default as LucideKanbanSquare } from '../icons/kanban-square.js';
// SquareLibrary aliases
export { default as LucideSquareLibrary } from '../icons/square-library.js';
export { default as LucideLibrarySquare } from '../icons/library-square.js';
// SquareM aliases
export { default as LucideSquareM } from '../icons/square-m.js';
export { default as LucideMSquare } from '../icons/m-square.js';
// SquareMenu aliases
export { default as LucideSquareMenu } from '../icons/square-menu.js';
export { default as LucideMenuSquare } from '../icons/menu-square.js';
// SquareMinus aliases
export { default as LucideSquareMinus } from '../icons/square-minus.js';
export { default as LucideMinusSquare } from '../icons/minus-square.js';
// SquareMousePointer aliases
export { default as LucideSquareMousePointer } from '../icons/square-mouse-pointer.js';
export { default as LucideInspect } from '../icons/inspect.js';
// SquareParkingOff aliases
export { default as LucideSquareParkingOff } from '../icons/square-parking-off.js';
export { default as LucideParkingSquareOff } from '../icons/parking-square-off.js';
// SquareParking aliases
export { default as LucideSquareParking } from '../icons/square-parking.js';
export { default as LucideParkingSquare } from '../icons/parking-square.js';
// SquarePen aliases
export { default as LucideSquarePen } from '../icons/square-pen.js';
export { default as LucidePenBox } from '../icons/pen-box.js';
export { default as LucideEdit } from '../icons/edit.js';
export { default as LucidePenSquare } from '../icons/pen-square.js';
// SquarePercent aliases
export { default as LucideSquarePercent } from '../icons/square-percent.js';
export { default as LucidePercentSquare } from '../icons/percent-square.js';
// SquarePilcrow aliases
export { default as LucideSquarePilcrow } from '../icons/square-pilcrow.js';
export { default as LucidePilcrowSquare } from '../icons/pilcrow-square.js';
// SquarePi aliases
export { default as LucideSquarePi } from '../icons/square-pi.js';
export { default as LucidePiSquare } from '../icons/pi-square.js';
// SquarePlay aliases
export { default as LucideSquarePlay } from '../icons/square-play.js';
export { default as LucidePlaySquare } from '../icons/play-square.js';
// SquarePlus aliases
export { default as LucideSquarePlus } from '../icons/square-plus.js';
export { default as LucidePlusSquare } from '../icons/plus-square.js';
// SquareScissors aliases
export { default as LucideSquareScissors } from '../icons/square-scissors.js';
export { default as LucideScissorsSquare } from '../icons/scissors-square.js';
// SquareSigma aliases
export { default as LucideSquareSigma } from '../icons/square-sigma.js';
export { default as LucideSigmaSquare } from '../icons/sigma-square.js';
// SquarePower aliases
export { default as LucideSquarePower } from '../icons/square-power.js';
export { default as LucidePowerSquare } from '../icons/power-square.js';
// SquareSlash aliases
export { default as LucideSquareSlash } from '../icons/square-slash.js';
export { default as LucideSlashSquare } from '../icons/slash-square.js';
// SquareSplitHorizontal aliases
export { default as LucideSquareSplitHorizontal } from '../icons/square-split-horizontal.js';
export { default as LucideSplitSquareHorizontal } from '../icons/split-square-horizontal.js';
// SquareSplitVertical aliases
export { default as LucideSquareSplitVertical } from '../icons/square-split-vertical.js';
export { default as LucideSplitSquareVertical } from '../icons/split-square-vertical.js';
// SquareTerminal aliases
export { default as LucideSquareTerminal } from '../icons/square-terminal.js';
export { default as LucideTerminalSquare } from '../icons/terminal-square.js';
// SquareUserRound aliases
export { default as LucideSquareUserRound } from '../icons/square-user-round.js';
export { 
/** @deprecated  */
default as LucideUserSquare2 } from '../icons/user-square-2.js';
// SquareUser aliases
export { default as LucideSquareUser } from '../icons/square-user.js';
export { default as LucideUserSquare } from '../icons/user-square.js';
// SquareX aliases
export { default as LucideSquareX } from '../icons/square-x.js';
export { default as LucideXSquare } from '../icons/x-square.js';
// TestTubeDiagonal aliases
export { default as LucideTestTubeDiagonal } from '../icons/test-tube-diagonal.js';
export { default as LucideTestTube2 } from '../icons/test-tube-2.js';
// TextSelect aliases
export { default as LucideTextSelect } from '../icons/text-select.js';
export { default as LucideTextSelection } from '../icons/text-selection.js';
// TramFront aliases
export { default as LucideTramFront } from '../icons/tram-front.js';
export { default as LucideTrain } from '../icons/train.js';
// TreePalm aliases
export { default as LucideTreePalm } from '../icons/tree-palm.js';
export { default as LucidePalmtree } from '../icons/palmtree.js';
// TriangleAlert aliases
export { default as LucideTriangleAlert } from '../icons/triangle-alert.js';
export { default as LucideAlertTriangle } from '../icons/alert-triangle.js';
// TvMinimal aliases
export { default as LucideTvMinimal } from '../icons/tv-minimal.js';
export { 
/** @deprecated  */
default as LucideTv2 } from '../icons/tv-2.js';
// University aliases
export { default as LucideUniversity } from '../icons/university.js';
export { 
/** @deprecated  */
default as LucideSchool2 } from '../icons/school-2.js';
// UserRoundCheck aliases
export { default as LucideUserRoundCheck } from '../icons/user-round-check.js';
export { 
/** @deprecated  */
default as LucideUserCheck2 } from '../icons/user-check-2.js';
// UserRoundCog aliases
export { default as LucideUserRoundCog } from '../icons/user-round-cog.js';
export { 
/** @deprecated  */
default as LucideUserCog2 } from '../icons/user-cog-2.js';
// UserRoundMinus aliases
export { default as LucideUserRoundMinus } from '../icons/user-round-minus.js';
export { 
/** @deprecated  */
default as LucideUserMinus2 } from '../icons/user-minus-2.js';
// UserRoundPlus aliases
export { default as LucideUserRoundPlus } from '../icons/user-round-plus.js';
export { 
/** @deprecated  */
default as LucideUserPlus2 } from '../icons/user-plus-2.js';
// UserRoundX aliases
export { default as LucideUserRoundX } from '../icons/user-round-x.js';
export { 
/** @deprecated  */
default as LucideUserX2 } from '../icons/user-x-2.js';
// UserRound aliases
export { default as LucideUserRound } from '../icons/user-round.js';
export { 
/** @deprecated  */
default as LucideUser2 } from '../icons/user-2.js';
// UsersRound aliases
export { default as LucideUsersRound } from '../icons/users-round.js';
export { 
/** @deprecated  */
default as LucideUsers2 } from '../icons/users-2.js';
// UtensilsCrossed aliases
export { default as LucideUtensilsCrossed } from '../icons/utensils-crossed.js';
export { default as LucideForkKnifeCrossed } from '../icons/fork-knife-crossed.js';
// Utensils aliases
export { default as LucideUtensils } from '../icons/utensils.js';
export { default as LucideForkKnife } from '../icons/fork-knife.js';
// WalletMinimal aliases
export { default as LucideWalletMinimal } from '../icons/wallet-minimal.js';
export { 
/** @deprecated  */
default as LucideWallet2 } from '../icons/wallet-2.js';
// WandSparkles aliases
export { default as LucideWandSparkles } from '../icons/wand-sparkles.js';
export { 
/** @deprecated  */
default as LucideWand2 } from '../icons/wand-2.js';
